package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.XiqingRoadshowRegistrationMapper;
import com.ruoyi.miniapp.domain.XiqingRoadshowRegistration;
import com.ruoyi.miniapp.service.IXiqingRoadshowRegistrationService;

/**
 * 西青金种子路演报名管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class XiqingRoadshowRegistrationServiceImpl implements IXiqingRoadshowRegistrationService 
{
    @Autowired
    private XiqingRoadshowRegistrationMapper xiqingRoadshowRegistrationMapper;

    /**
     * 查询西青金种子路演报名管理
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 西青金种子路演报名管理
     */
    @Override
    public XiqingRoadshowRegistration selectXiqingRoadshowRegistrationByRegistrationId(Long registrationId)
    {
        return xiqingRoadshowRegistrationMapper.selectXiqingRoadshowRegistrationByRegistrationId(registrationId);
    }

    /**
     * 查询西青金种子路演报名管理列表
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 西青金种子路演报名管理
     */
    @Override
    public List<XiqingRoadshowRegistration> selectXiqingRoadshowRegistrationList(XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        return xiqingRoadshowRegistrationMapper.selectXiqingRoadshowRegistrationList(xiqingRoadshowRegistration);
    }

    /**
     * 新增西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    @Override
    public int insertXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        xiqingRoadshowRegistration.setCreateTime(DateUtils.getNowDate());
        return xiqingRoadshowRegistrationMapper.insertXiqingRoadshowRegistration(xiqingRoadshowRegistration);
    }

    /**
     * 修改西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    @Override
    public int updateXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        xiqingRoadshowRegistration.setUpdateTime(DateUtils.getNowDate());
        return xiqingRoadshowRegistrationMapper.updateXiqingRoadshowRegistration(xiqingRoadshowRegistration);
    }

    /**
     * 批量删除西青金种子路演报名管理
     * 
     * @param registrationIds 需要删除的西青金种子路演报名管理主键
     * @return 结果
     */
    @Override
    public int deleteXiqingRoadshowRegistrationByRegistrationIds(Long[] registrationIds)
    {
        return xiqingRoadshowRegistrationMapper.deleteXiqingRoadshowRegistrationByRegistrationIds(registrationIds);
    }

    /**
     * 删除西青金种子路演报名管理信息
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 结果
     */
    @Override
    public int deleteXiqingRoadshowRegistrationByRegistrationId(Long registrationId)
    {
        return xiqingRoadshowRegistrationMapper.deleteXiqingRoadshowRegistrationByRegistrationId(registrationId);
    }
}
