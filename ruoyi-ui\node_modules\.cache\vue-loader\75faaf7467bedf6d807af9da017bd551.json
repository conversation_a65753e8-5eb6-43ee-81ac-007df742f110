{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753755064715}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752653991061}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEFjdGl2aXR5Q29uZmlnLCB1cGRhdGVBY3Rpdml0eUNvbmZpZyB9IGZyb20gIkAvYXBpL21pbmlhcHAveGlxaW5nL2FjdGl2aXR5LWNvbmZpZyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlhpcWluZ0FjdGl2aXR5Q29uZmlnIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYr+WQpuaYvuekuuihqOWNlemFjee9ruW8ueWHuuWxggogICAgICBmb3JtQ29uZmlnT3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaYvuekuuihqOWNlemihOiniOW8ueWHuuWxggogICAgICBwcmV2aWV3RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOihqOWNleWtl+autemFjee9rgogICAgICBmb3JtRmllbGRzOiBbXSwKICAgICAgLy8g5rS75YqoSUTvvIjlm7rlrprkuLox77yM5Zug5Li65Y+q5pyJ5LiA5Liq6Lev5ryU5rS75Yqo6YWN572u77yJCiAgICAgIGFjdGl2aXR5SWQ6IDEKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOWKoOi9veihqOWNlemFjee9riAqLwogICAgbG9hZEZvcm1Db25maWcoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldEFjdGl2aXR5Q29uZmlnKHRoaXMuYWN0aXZpdHlJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBKU09OLnBhcnNlKHJlc3BvbnNlLmRhdGEuZm9ybUNvbmZpZyk7CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IFtdOwogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsKICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRm9ybUNvbmZpZygpIHsKICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOa3u+WKoOihqOWNleWtl+autSAqLwogICAgYWRkRm9ybUZpZWxkKCkgewogICAgICB0aGlzLmZvcm1GaWVsZHMucHVzaCh7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgbGFiZWw6ICcnLAogICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICAgIG9wdGlvbnM6ICcnCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTooajljZXlrZfmrrUgKi8KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgewogICAgICB0aGlzLmZvcm1GaWVsZHMuc3BsaWNlKGluZGV4LCAxKTsKICAgIH0sCiAgICAvKiog56e75Yqo5a2X5q615L2N572uICovCiAgICBtb3ZlRmllbGQoaW5kZXgsIGRpcmVjdGlvbikgewogICAgICBjb25zdCBuZXdJbmRleCA9IGluZGV4ICsgZGlyZWN0aW9uOwogICAgICBpZiAobmV3SW5kZXggPj0gMCAmJiBuZXdJbmRleCA8IHRoaXMuZm9ybUZpZWxkcy5sZW5ndGgpIHsKICAgICAgICBjb25zdCB0ZW1wID0gdGhpcy5mb3JtRmllbGRzW2luZGV4XTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtRmllbGRzLCBpbmRleCwgdGhpcy5mb3JtRmllbGRzW25ld0luZGV4XSk7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybUZpZWxkcywgbmV3SW5kZXgsIHRlbXApOwogICAgICB9CiAgICB9LAogICAgLyoqIOabtOaWsOWtl+auteWQjeensCAqLwogICAgdXBkYXRlRmllbGROYW1lKGZpZWxkLCBsYWJlbCkgewogICAgICBpZiAoIWZpZWxkLm5hbWUgfHwgZmllbGQubmFtZSA9PT0gJycpIHsKICAgICAgICBmaWVsZC5uYW1lID0gdGhpcy5nZW5lcmF0ZUZpZWxkTmFtZShsYWJlbCk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog55Sf5oiQ5a2X5q615ZCN56ewICovCiAgICBnZW5lcmF0ZUZpZWxkTmFtZShsYWJlbCkgewogICAgICBjb25zdCBwaW55aW4gPSB7CiAgICAgICAgJ+Wnk+WQjSc6ICduYW1lJywKICAgICAgICAn6IGU57O755S16K+dJzogJ3Bob25lJywKICAgICAgICAn55S16K+dJzogJ3Bob25lJywKICAgICAgICAn6YKu566xJzogJ2VtYWlsJywKICAgICAgICAn6YKu566x5Zyw5Z2AJzogJ2VtYWlsJywKICAgICAgICAn5YWs5Y+4JzogJ2NvbXBhbnknLAogICAgICAgICfpobnnm67lkI3np7AnOiAncHJvamVjdF9uYW1lJywKICAgICAgICAn6aG555uu5o+P6L+wJzogJ3Byb2plY3RfZGVzY3JpcHRpb24nLAogICAgICAgICflm6LpmJ/op4TmqKEnOiAndGVhbV9zaXplJwogICAgICB9OwogICAgICByZXR1cm4gcGlueWluW2xhYmVsXSB8fCBsYWJlbC50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xzKy9nLCAnXycpOwogICAgfSwKICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8KICAgIGdldEZpZWxkSWNvbih0eXBlKSB7CiAgICAgIGNvbnN0IGljb25zID0gewogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywKICAgICAgICB0ZXh0YXJlYTogJ2VsLWljb24tZG9jdW1lbnQnLAogICAgICAgIG51bWJlcjogJ2VsLWljb24tcy1kYXRhJywKICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsCiAgICAgICAgdGVsOiAnZWwtaWNvbi1waG9uZScsCiAgICAgICAgcmFkaW86ICdlbC1pY29uLXN1Y2Nlc3MnLAogICAgICAgIGNoZWNrYm94OiAnZWwtaWNvbi1jaGVjaycsCiAgICAgICAgc2VsZWN0OiAnZWwtaWNvbi1hcnJvdy1kb3duJywKICAgICAgICByYWRpb19vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMnLAogICAgICAgIGNoZWNrYm94X290aGVyOiAnZWwtaWNvbi1zcXVhcmUtcGx1cycsCiAgICAgICAgc2VsZWN0X290aGVyOiAnZWwtaWNvbi1wbHVzJywKICAgICAgICBkYXRlOiAnZWwtaWNvbi1kYXRlJywKICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnCiAgICAgIH07CiAgICAgIHJldHVybiBpY29uc1t0eXBlXSB8fCAnZWwtaWNvbi1lZGl0JzsKICAgIH0sCiAgICAvKiog5aSE55CG5qih5p2/5ZG95LukICovCiAgICBoYW5kbGVUZW1wbGF0ZUNvbW1hbmQoY29tbWFuZCkgewogICAgICBpZiAoY29tbWFuZCA9PT0gJ2NsZWFyJykgewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgea4heepuuaJgOacieWtl+auteWQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3sua4heepuuaJgOacieWtl+autScpOwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgY29uc3QgdGVtcGxhdGVzID0gewogICAgICAgIGJhc2ljOiBbCiAgICAgICAgICB7IGxhYmVsOiAn5aeT5ZCNJywgbmFtZTogJ25hbWUnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAncGhvbmUnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6YKu566x5Zyw5Z2AJywgbmFtZTogJ2VtYWlsJywgdHlwZTogJ2VtYWlsJywgcmVxdWlyZWQ6IGZhbHNlLCBvcHRpb25zOiAnJyB9CiAgICAgICAgXSwKICAgICAgICByb2Fkc2hvdzogWwogICAgICAgICAgeyBsYWJlbDogJ+Wnk+WQjScsIG5hbWU6ICduYW1lJywgdHlwZTogJ2lucHV0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6IGU57O755S16K+dJywgbmFtZTogJ3Bob25lJywgdHlwZTogJ3RlbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICdlbWFpbCcsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+WFrOWPuC/lm6LpmJ8nLCBuYW1lOiAnY29tcGFueScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+mhueebruWQjeensCcsIG5hbWU6ICdwcm9qZWN0X25hbWUnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67mnaXmupAnLCBuYW1lOiAncHJvamVjdF9zb3VyY2UnLCB0eXBlOiAncmFkaW9fb3RoZXInLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+ekvuS8mizpq5jmoKEs56eR56CU6Zmi5omALOS8geS4muWGhemDqCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67pmLbmrrUnLCBuYW1lOiAncHJvamVjdF9zdGFnZScsIHR5cGU6ICdyYWRpbycsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAn5qaC5b+16Zi25q61LOW8gOWPkemYtuautSzmtYvor5XpmLbmrrUs5LiK57q/6Zi25q61JyB9LAogICAgICAgICAgeyBsYWJlbDogJ+WboumYn+inhOaooScsIG5hbWU6ICd0ZWFtX3NpemUnLCB0eXBlOiAnc2VsZWN0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcx5Lq6LDItM+S6uiw0LTXkurosNi0xMOS6uiwxMOS6uuS7peS4iicgfSwKICAgICAgICAgIHsgbGFiZWw6ICfono3otYTpnIDmsYInLCBuYW1lOiAnZnVuZGluZ19uZWVkcycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfllYbkuJrorqHliJLkuaYnLCBuYW1lOiAnYnVzaW5lc3NfcGxhbicsIHR5cGU6ICdmaWxlJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu5o+P6L+wJywgbmFtZTogJ3Byb2plY3RfZGVzY3JpcHRpb24nLCB0eXBlOiAndGV4dGFyZWEnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfQogICAgICAgIF0KICAgICAgfTsKCiAgICAgIGlmICh0ZW1wbGF0ZXNbY29tbWFuZF0pIHsKICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSB0ZW1wbGF0ZXNbY29tbWFuZF07CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmqKHmnb/lupTnlKjmiJDlip8nKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDpooTop4jooajljZUgKi8KICAgIHByZXZpZXdGb3JtKCkgewogICAgICBpZiAodGhpcy5mb3JtRmllbGRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI5re75Yqg6KGo5Y2V5a2X5q61Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8qKiDkv53lrZjooajljZXphY3nva4gKi8KICAgIHNhdmVGb3JtQ29uZmlnKCkgewogICAgICBpZiAodGhpcy5mb3JtRmllbGRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR5re75Yqg5LiA5Liq6KGo5Y2V5a2X5q61Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDpqozor4HlrZfmrrXphY3nva4KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1GaWVsZHMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBmaWVsZCA9IHRoaXMuZm9ybUZpZWxkc1tpXTsKICAgICAgICBpZiAoIWZpZWxkLmxhYmVsKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBpZiAoWydyYWRpbycsICdjaGVja2JveCcsICdzZWxlY3QnLCAncmFkaW9fb3RoZXInLCAnY2hlY2tib3hfb3RoZXInLCAnc2VsZWN0X290aGVyJ10uaW5jbHVkZXMoZmllbGQudHlwZSkgJiYgIWZpZWxkLm9wdGlvbnMpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOWtl+autSIke2ZpZWxkLmxhYmVsfSLpnIDopoHphY3nva7pgInpoblgKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm1GaWVsZHMpOwogICAgICBjb25zdCB1cGRhdGVEYXRhID0gewogICAgICAgIGFjdGl2aXR5SWQ6IHRoaXMuYWN0aXZpdHlJZCwKICAgICAgICBmb3JtQ29uZmlnOiBjb25maWdEYXRhCiAgICAgIH07CgogICAgICB1cGRhdGVBY3Rpdml0eUNvbmZpZyh1cGRhdGVEYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLooajljZXphY3nva7kv53lrZjmiJDlip8iKTsKICAgICAgICB0aGlzLmZvcm1Db25maWdPcGVuID0gZmFsc2U7CiAgICAgICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5ZCN56ewICovCiAgICBnZXRGaWVsZFR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU5hbWVzID0gewogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywKICAgICAgICB0ZXh0YXJlYTogJ+WkmuihjOaWh+acrCcsCiAgICAgICAgbnVtYmVyOiAn5pWw5a2X6L6T5YWlJywKICAgICAgICBlbWFpbDogJ+mCrueusScsCiAgICAgICAgdGVsOiAn55S16K+dJywKICAgICAgICByYWRpbzogJ+WNlemAiScsCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLAogICAgICAgIHNlbGVjdDogJ+S4i+aLiemAieaLqScsCiAgICAgICAgcmFkaW9fb3RoZXI6ICfljZXpgIkr5YW25LuWJywKICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLAogICAgICAgIHNlbGVjdF9vdGhlcjogJ+S4i+aLiSvlhbbku5YnLAogICAgICAgIGRhdGU6ICfml6XmnJ8nLAogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTmFtZXNbdHlwZV0gfHwgJ+acquefpeexu+Weiyc7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/activity-config", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-config-container\">\n      <div class=\"config-header\">\n        <h3>路演活动报名表单配置</h3>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-setting\"\n          @click=\"handleFormConfig\"\n          v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\n        >配置表单</el-button>\n      </div>\n\n      <div class=\"config-content\" v-loading=\"loading\">\n        <div v-if=\"formFields.length > 0\" class=\"form-preview\">\n          <h4>当前表单字段预览：</h4>\n          <div class=\"field-list\">\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-item\">\n              <div class=\"field-info\">\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                <span class=\"field-label\">{{ field.label }}</span>\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n                <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div v-else class=\"empty-form\">\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂未配置表单字段，点击\"配置表单\"开始设置</p>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 表单配置对话框 -->\n    <el-dialog title=\"报名表单配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\n      <div class=\"form-fields-config\">\n        <!-- 工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\n              <el-button size=\"small\">\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\n              预览表单\n            </el-button>\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\n              保存配置\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 字段配置区域 -->\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\n          <div\n            v-for=\"(field, index) in formFields\"\n            :key=\"index\"\n            class=\"form-field-item\"\n          >\n            <div class=\"field-header\">\n              <div class=\"field-info\">\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                <span class=\"field-label\">{{ field.label || '未命名字段' }}</span>\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n              </div>\n              <div class=\"field-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"moveField(index, -1)\"\n                  :disabled=\"index === 0\"\n                  icon=\"el-icon-arrow-up\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"moveField(index, 1)\"\n                  :disabled=\"index === formFields.length - 1\"\n                  icon=\"el-icon-arrow-down\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"removeFormField(index)\"\n                  icon=\"el-icon-delete\"\n                  class=\"danger-btn\"\n                ></el-button>\n              </div>\n            </div>\n\n            <div class=\"field-content\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段标签</label>\n                    <el-input\n                      v-model=\"field.label\"\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\n                      size=\"small\"\n                      @input=\"updateFieldName(field, $event)\"\n                    />\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段类型</label>\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\n                      <el-option label=\"📞 电话\" value=\"tel\" />\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                      <el-option label=\"📅 日期\" value=\"date\" />\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\n                    </el-select>\n                  </div>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\n                <el-col :span=\"4\">\n                  <div class=\"field-item\">\n                    <label>是否必填</label>\n                    <el-switch v-model=\"field.required\" />\n                  </div>\n                </el-col>\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\n                  <div class=\"field-item\">\n                    <label>选项配置</label>\n                    <el-input\n                      v-model=\"field.options\"\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\n                      size=\"small\"\n                    />\n                    <div class=\"options-preview\" v-if=\"field.options\">\n                      <el-tag\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\n                        :key=\"optIndex\"\n                        size=\"mini\"\n                        style=\"margin-right: 5px; margin-top: 5px;\"\n                      >\n                        {{ option.trim() }}\n                      </el-tag>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div class=\"empty-state\" v-else>\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <el-input\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input-number\n                v-else-if=\"field.type === 'number'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-radio>\n              </el-radio-group>\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-checkbox>\n              </el-checkbox-group>\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\n                <el-option\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                  :value=\"option.trim()\"\n                />\n              </el-select>\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :disabled=\"true\"\n                :show-file-list=\"false\"\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>\n                  <i class=\"el-icon-upload\"></i> 选择文件\n                </el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\n              </el-upload>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-radio>\n                  </div>\n                </el-radio-group>\n              </div>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-checkbox>\n                  </div>\n                </el-checkbox-group>\n              </div>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\n                  size=\"small\"\n                  disabled\n                  style=\"width: 100%;\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\n\nexport default {\n  name: \"XiqingActivityConfig\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 表单字段配置\n      formFields: [],\n      // 活动ID（固定为1，因为只有一个路演活动配置）\n      activityId: 1\n    };\n  },\n  created() {\n    this.loadFormConfig();\n  },\n  methods: {\n    /** 加载表单配置 */\n    loadFormConfig() {\n      this.loading = true;\n      getActivityConfig(this.activityId).then(response => {\n        if (response.data && response.data.formConfig) {\n          try {\n            this.formFields = JSON.parse(response.data.formConfig);\n          } catch (e) {\n            this.formFields = [];\n          }\n        } else {\n          this.formFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.formFields = [];\n        this.loading = false;\n      });\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig() {\n      this.formConfigOpen = true;\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      this.formFields.push({\n        name: '',\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.formFields.splice(index, 1);\n    },\n    /** 移动字段位置 */\n    moveField(index, direction) {\n      const newIndex = index + direction;\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\n        const temp = this.formFields[index];\n        this.$set(this.formFields, index, this.formFields[newIndex]);\n        this.$set(this.formFields, newIndex, temp);\n      }\n    },\n    /** 更新字段名称 */\n    updateFieldName(field, label) {\n      if (!field.name || field.name === '') {\n        field.name = this.generateFieldName(label);\n      }\n    },\n    /** 生成字段名称 */\n    generateFieldName(label) {\n      const pinyin = {\n        '姓名': 'name',\n        '联系电话': 'phone',\n        '电话': 'phone',\n        '邮箱': 'email',\n        '邮箱地址': 'email',\n        '公司': 'company',\n        '项目名称': 'project_name',\n        '项目描述': 'project_description',\n        '团队规模': 'team_size'\n      };\n      return pinyin[label] || label.toLowerCase().replace(/\\s+/g, '_');\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-success',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus',\n        checkbox_other: 'el-icon-square-plus',\n        select_other: 'el-icon-plus',\n        date: 'el-icon-date',\n        file: 'el-icon-upload'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$confirm('确定要清空所有字段吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.formFields = [];\n          this.$message.success('已清空所有字段');\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }\n        ],\n        roadshow: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },\n          { label: '公司/团队', name: 'company', type: 'input', required: true, options: '' },\n          { label: '项目名称', name: 'project_name', type: 'input', required: true, options: '' },\n          { label: '项目来源', name: 'project_source', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\n          { label: '项目阶段', name: 'project_stage', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\n          { label: '团队规模', name: 'team_size', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '融资需求', name: 'funding_needs', type: 'input', required: false, options: '' },\n          { label: '商业计划书', name: 'business_plan', type: 'file', required: true, options: '' },\n          { label: '项目描述', name: 'project_description', type: 'textarea', required: true, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.formFields = templates[command];\n        this.$message.success('模板应用成功');\n      }\n    },\n    /** 预览表单 */\n    previewForm() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请先添加表单字段');\n        return;\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请至少添加一个表单字段');\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.formFields.length; i++) {\n        const field = this.formFields[i];\n        if (!field.label) {\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\n          return;\n        }\n      }\n\n      const configData = JSON.stringify(this.formFields);\n      const updateData = {\n        activityId: this.activityId,\n        formConfig: configData\n      };\n\n      updateActivityConfig(updateData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadFormConfig();\n      });\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const typeNames = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        file: '文件上传'\n      };\n      return typeNames[type] || '未知类型';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  max-width: 800px;\n  margin: 20px auto;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 300px;\n}\n\n.form-preview h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n  min-width: 100px;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n}\n\n.empty-form {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-form i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-fields-config {\n  min-height: 400px;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 10px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.toolbar-left, .toolbar-right {\n  display: flex;\n  gap: 10px;\n}\n\n.form-fields-list {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.form-field-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  background: #fff;\n  transition: all 0.3s;\n}\n\n.form-field-item:hover {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n.field-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #e4e7ed;\n  border-radius: 6px 6px 0 0;\n}\n\n.field-actions {\n  display: flex;\n  gap: 5px;\n}\n\n.danger-btn {\n  color: #f56c6c;\n}\n\n.field-content {\n  padding: 16px;\n}\n\n.field-item {\n  margin-bottom: 10px;\n}\n\n.field-item label {\n  display: block;\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #606266;\n  font-weight: 500;\n}\n\n.options-preview {\n  margin-top: 5px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-preview {\n  padding: 20px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.preview-header h3 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 2px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"]}]}