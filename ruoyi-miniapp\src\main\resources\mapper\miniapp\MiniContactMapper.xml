<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniContactMapper">
    
    <resultMap type="MiniContact" id="MiniContactResult">
        <result property="contactId"    column="contact_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactCode"    column="contact_code"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="qrCodeUrl"    column="qr_code_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniContactVo">
        select contact_id, contact_name, contact_code, contact_phone, qr_code_url, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_contact
    </sql>

    <select id="selectMiniContactList" parameterType="MiniContact" resultMap="MiniContactResult">
        <include refid="selectMiniContactVo"/>
        <where>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactCode != null  and contactCode != ''"> and contact_code = #{contactCode}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniContactByContactId" parameterType="Long" resultMap="MiniContactResult">
        <include refid="selectMiniContactVo"/>
        where contact_id = #{contactId}
    </select>

    <select id="selectEnabledMiniContactList" resultMap="MiniContactResult">
        <include refid="selectMiniContactVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniContact" parameterType="MiniContact" useGeneratedKeys="true" keyProperty="contactId">
        insert into mini_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactCode != null and contactCode != ''">contact_code,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="qrCodeUrl != null and qrCodeUrl != ''">qr_code_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactCode != null and contactCode != ''">#{contactCode},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="qrCodeUrl != null and qrCodeUrl != ''">#{qrCodeUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateMiniContact" parameterType="MiniContact">
        update mini_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactCode != null and contactCode != ''">contact_code = #{contactCode},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="qrCodeUrl != null">qr_code_url = #{qrCodeUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where contact_id = #{contactId}
    </update>

    <update id="updateMiniContactSort" parameterType="MiniContact">
        update mini_contact set sort_order = #{sortOrder}, update_by = #{updateBy}, update_time = #{updateTime}
        where contact_id = #{contactId}
    </update>

    <delete id="deleteMiniContactByContactId" parameterType="Long">
        delete from mini_contact where contact_id = #{contactId}
    </delete>

    <delete id="deleteMiniContactByContactIds" parameterType="String">
        delete from mini_contact where contact_id in
        <foreach item="contactId" collection="array" open="(" separator="," close=")">
            #{contactId}
        </foreach>
    </delete>

    <select id="selectMiniContactByContactCode" parameterType="String" resultMap="MiniContactResult">
        <include refid="selectMiniContactVo"/>
        where contact_code = #{contactCode} and status = '0'
        limit 1
    </select>
</mapper>
