package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.XiqingRoadshowActivityMapper;
import com.ruoyi.miniapp.domain.XiqingRoadshowActivity;
import com.ruoyi.miniapp.service.IXiqingRoadshowActivityService;

/**
 * 西青金种子路演活动配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class XiqingRoadshowActivityServiceImpl implements IXiqingRoadshowActivityService 
{
    @Autowired
    private XiqingRoadshowActivityMapper xiqingRoadshowActivityMapper;

    /**
     * 查询西青金种子路演活动配置
     * 
     * @param activityId 西青金种子路演活动配置主键
     * @return 西青金种子路演活动配置
     */
    @Override
    public XiqingRoadshowActivity selectXiqingRoadshowActivityByActivityId(Long activityId)
    {
        return xiqingRoadshowActivityMapper.selectXiqingRoadshowActivityByActivityId(activityId);
    }

    /**
     * 查询西青金种子路演活动配置列表
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 西青金种子路演活动配置
     */
    @Override
    public List<XiqingRoadshowActivity> selectXiqingRoadshowActivityList(XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        return xiqingRoadshowActivityMapper.selectXiqingRoadshowActivityList(xiqingRoadshowActivity);
    }

    /**
     * 新增西青金种子路演活动配置
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 结果
     */
    @Override
    public int insertXiqingRoadshowActivity(XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        xiqingRoadshowActivity.setCreateTime(DateUtils.getNowDate());
        return xiqingRoadshowActivityMapper.insertXiqingRoadshowActivity(xiqingRoadshowActivity);
    }

    /**
     * 修改西青金种子路演活动配置
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 结果
     */
    @Override
    public int updateXiqingRoadshowActivity(XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        xiqingRoadshowActivity.setUpdateTime(DateUtils.getNowDate());
        return xiqingRoadshowActivityMapper.updateXiqingRoadshowActivity(xiqingRoadshowActivity);
    }

    /**
     * 批量删除西青金种子路演活动配置
     * 
     * @param activityIds 需要删除的西青金种子路演活动配置主键
     * @return 结果
     */
    @Override
    public int deleteXiqingRoadshowActivityByActivityIds(Long[] activityIds)
    {
        return xiqingRoadshowActivityMapper.deleteXiqingRoadshowActivityByActivityIds(activityIds);
    }

    /**
     * 删除西青金种子路演活动配置信息
     * 
     * @param activityId 西青金种子路演活动配置主键
     * @return 结果
     */
    @Override
    public int deleteXiqingRoadshowActivityByActivityId(Long activityId)
    {
        return xiqingRoadshowActivityMapper.deleteXiqingRoadshowActivityByActivityId(activityId);
    }

    /**
     * 查询启用的路演活动列表（小程序端）
     * 
     * @return 西青金种子路演活动配置集合
     */
    @Override
    public List<XiqingRoadshowActivity> selectEnabledXiqingRoadshowActivityList()
    {
        return xiqingRoadshowActivityMapper.selectEnabledXiqingRoadshowActivityList();
    }

    /**
     * 增加活动报名人数
     * 
     * @param activityId 活动ID
     * @return 结果
     */
    @Override
    public int incrementParticipantCount(Long activityId)
    {
        return xiqingRoadshowActivityMapper.incrementParticipantCount(activityId);
    }
}
