{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=style&index=0&id=733e5256&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753754989841}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752653987088}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752653998445}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752653991020}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZvcm0tY29uZmlnLWNvbnRhaW5lciB7CiAgbWF4LXdpZHRoOiA4MDBweDsKICBtYXJnaW46IDIwcHggYXV0bzsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5jb25maWctaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDIwcHggMjRweDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOwp9CgouY29uZmlnLWhlYWRlciBoMyB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtc2l6ZTogMThweDsKICBmb250LXdlaWdodDogNTAwOwp9CgouY29uZmlnLWNvbnRlbnQgewogIHBhZGRpbmc6IDI0cHg7CiAgbWluLWhlaWdodDogMzAwcHg7Cn0KCi5mb3JtLXByZXZpZXcgaDQgewogIG1hcmdpbjogMCAwIDIwcHggMDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXNpemU6IDE2cHg7Cn0KCi5maWVsZC1saXN0IHsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouZmllbGQtaXRlbSB7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZiZmM7Cn0KCi5maWVsZC1pdGVtOmxhc3QtY2hpbGQgewogIGJvcmRlci1ib3R0b206IG5vbmU7Cn0KCi5maWVsZC1pbmZvIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxMnB4Owp9CgouZmllbGQtaWNvbiB7CiAgY29sb3I6ICM0MDllZmY7CiAgZm9udC1zaXplOiAxNnB4Owp9CgouZmllbGQtbGFiZWwgewogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgbWluLXdpZHRoOiAxMDBweDsKfQoKLmZpZWxkLXR5cGUgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTJweDsKICBtYXJnaW4tbGVmdDogYXV0bzsKfQoKLmVtcHR5LWZvcm0gewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA2MHB4IDIwcHg7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi5lbXB0eS1mb3JtIGkgewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGRpc3BsYXk6IGJsb2NrOwp9CgouZm9ybS1maWVsZHMtY29uZmlnIHsKICBtaW4taGVpZ2h0OiA0MDBweDsKfQoKLmZvcm0tZmllbGRzLXRvb2xiYXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBwYWRkaW5nOiAxMHB4OwogIGJhY2tncm91bmQ6ICNmNWY3ZmE7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgoudG9vbGJhci1sZWZ0LCAudG9vbGJhci1yaWdodCB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEwcHg7Cn0KCi5mb3JtLWZpZWxkcy1saXN0IHsKICBtYXgtaGVpZ2h0OiA1MDBweDsKICBvdmVyZmxvdy15OiBhdXRvOwp9CgouZm9ybS1maWVsZC1pdGVtIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7Cn0KCi5mb3JtLWZpZWxkLWl0ZW06aG92ZXIgewogIGJvcmRlci1jb2xvcjogIzQwOWVmZjsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7Cn0KCi5maWVsZC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIGJhY2tncm91bmQ6ICNmYWZiZmM7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNnB4IDZweCAwIDA7Cn0KCi5maWVsZC1hY3Rpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogNXB4Owp9CgouZGFuZ2VyLWJ0biB7CiAgY29sb3I6ICNmNTZjNmM7Cn0KCi5maWVsZC1jb250ZW50IHsKICBwYWRkaW5nOiAxNnB4Owp9CgouZmllbGQtaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLmZpZWxkLWl0ZW0gbGFiZWwgewogIGRpc3BsYXk6IGJsb2NrOwogIG1hcmdpbi1ib3R0b206IDVweDsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLm9wdGlvbnMtcHJldmlldyB7CiAgbWFyZ2luLXRvcDogNXB4Owp9CgouZW1wdHktc3RhdGUgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA2MHB4IDIwcHg7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi5lbXB0eS1zdGF0ZSBpIHsKICBmb250LXNpemU6IDQ4cHg7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKICBkaXNwbGF5OiBibG9jazsKfQoKLmZvcm0tcHJldmlldyB7CiAgcGFkZGluZzogMjBweDsKfQoKLnByZXZpZXctaGVhZGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKICBwYWRkaW5nLWJvdHRvbTogMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKfQoKLnByZXZpZXctaGVhZGVyIGgzIHsKICBtYXJnaW46IDAgMCAxMHB4IDA7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5wcmV2aWV3LWhlYWRlciBwIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zaXplOiAxNHB4Owp9CgoucHJldmlldy1maWVsZCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnByZXZpZXctbGFiZWwgewogIGRpc3BsYXk6IGJsb2NrOwogIG1hcmdpbi1ib3R0b206IDhweDsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjNjA2MjY2Owp9CgoucmVxdWlyZWQgewogIGNvbG9yOiAjZjU2YzZjOwogIG1hcmdpbi1sZWZ0OiAycHg7Cn0KCi5wcmV2aWV3LWlucHV0IHsKICB3aWR0aDogMTAwJTsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/activity-config", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-config-container\">\n      <div class=\"config-header\">\n        <h3>路演活动报名表单配置</h3>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-setting\"\n          @click=\"handleFormConfig\"\n          v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\n        >配置表单</el-button>\n      </div>\n\n      <div class=\"config-content\" v-loading=\"loading\">\n        <div v-if=\"formFields.length > 0\" class=\"form-preview\">\n          <h4>当前表单字段预览：</h4>\n          <div class=\"field-list\">\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-item\">\n              <div class=\"field-info\">\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                <span class=\"field-label\">{{ field.label }}</span>\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n                <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div v-else class=\"empty-form\">\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂未配置表单字段，点击\"配置表单\"开始设置</p>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 表单配置对话框 -->\n    <el-dialog title=\"报名表单配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\n      <div class=\"form-fields-config\">\n        <!-- 工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\n              <el-button size=\"small\">\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\n              预览表单\n            </el-button>\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\n              保存配置\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 字段配置区域 -->\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\n          <div\n            v-for=\"(field, index) in formFields\"\n            :key=\"index\"\n            class=\"form-field-item\"\n          >\n            <div class=\"field-header\">\n              <div class=\"field-info\">\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                <span class=\"field-label\">{{ field.label || '未命名字段' }}</span>\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\n              </div>\n              <div class=\"field-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"moveField(index, -1)\"\n                  :disabled=\"index === 0\"\n                  icon=\"el-icon-arrow-up\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"moveField(index, 1)\"\n                  :disabled=\"index === formFields.length - 1\"\n                  icon=\"el-icon-arrow-down\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"removeFormField(index)\"\n                  icon=\"el-icon-delete\"\n                  class=\"danger-btn\"\n                ></el-button>\n              </div>\n            </div>\n\n            <div class=\"field-content\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段标签</label>\n                    <el-input\n                      v-model=\"field.label\"\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\n                      size=\"small\"\n                      @input=\"updateFieldName(field, $event)\"\n                    />\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段类型</label>\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\n                      <el-option label=\"📞 电话\" value=\"tel\" />\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                      <el-option label=\"📅 日期\" value=\"date\" />\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\n                    </el-select>\n                  </div>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\n                <el-col :span=\"4\">\n                  <div class=\"field-item\">\n                    <label>是否必填</label>\n                    <el-switch v-model=\"field.required\" />\n                  </div>\n                </el-col>\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\n                  <div class=\"field-item\">\n                    <label>选项配置</label>\n                    <el-input\n                      v-model=\"field.options\"\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\n                      size=\"small\"\n                    />\n                    <div class=\"options-preview\" v-if=\"field.options\">\n                      <el-tag\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\n                        :key=\"optIndex\"\n                        size=\"mini\"\n                        style=\"margin-right: 5px; margin-top: 5px;\"\n                      >\n                        {{ option.trim() }}\n                      </el-tag>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div class=\"empty-state\" v-else>\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <el-input\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input-number\n                v-else-if=\"field.type === 'number'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-radio>\n              </el-radio-group>\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-checkbox>\n              </el-checkbox-group>\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\n                <el-option\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                  :value=\"option.trim()\"\n                />\n              </el-select>\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :disabled=\"true\"\n                :show-file-list=\"false\"\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>\n                  <i class=\"el-icon-upload\"></i> 选择文件\n                </el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\n              </el-upload>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-radio>\n                  </div>\n                </el-radio-group>\n              </div>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-checkbox>\n                  </div>\n                </el-checkbox-group>\n              </div>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\n                  size=\"small\"\n                  disabled\n                  style=\"width: 100%;\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\n\nexport default {\n  name: \"XiqingActivityConfig\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 表单字段配置\n      formFields: [],\n      // 活动ID（固定为1，因为只有一个路演活动配置）\n      activityId: 1\n    };\n  },\n  created() {\n    this.loadFormConfig();\n  },\n  methods: {\n    /** 加载表单配置 */\n    loadFormConfig() {\n      this.loading = true;\n      getActivityConfig(this.activityId).then(response => {\n        if (response.data && response.data.formConfig) {\n          try {\n            this.formFields = JSON.parse(response.data.formConfig);\n          } catch (e) {\n            this.formFields = [];\n          }\n        } else {\n          this.formFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.formFields = [];\n        this.loading = false;\n      });\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig() {\n      this.formConfigOpen = true;\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      this.formFields.push({\n        name: '',\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.formFields.splice(index, 1);\n    },\n    /** 移动字段位置 */\n    moveField(index, direction) {\n      const newIndex = index + direction;\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\n        const temp = this.formFields[index];\n        this.$set(this.formFields, index, this.formFields[newIndex]);\n        this.$set(this.formFields, newIndex, temp);\n      }\n    },\n    /** 更新字段名称 */\n    updateFieldName(field, label) {\n      if (!field.name || field.name === '') {\n        field.name = this.generateFieldName(label);\n      }\n    },\n    /** 生成字段名称 */\n    generateFieldName(label) {\n      const pinyin = {\n        '姓名': 'name',\n        '联系电话': 'phone',\n        '电话': 'phone',\n        '邮箱': 'email',\n        '邮箱地址': 'email',\n        '公司': 'company',\n        '项目名称': 'project_name',\n        '项目描述': 'project_description',\n        '团队规模': 'team_size'\n      };\n      return pinyin[label] || label.toLowerCase().replace(/\\s+/g, '_');\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-success',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        date: 'el-icon-date',\n        file: 'el-icon-upload'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$confirm('确定要清空所有字段吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.formFields = [];\n          this.$message.success('已清空所有字段');\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }\n        ],\n        roadshow: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },\n          { label: '公司/团队', name: 'company', type: 'input', required: true, options: '' },\n          { label: '项目名称', name: 'project_name', type: 'input', required: true, options: '' },\n          { label: '项目阶段', name: 'project_stage', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\n          { label: '团队规模', name: 'team_size', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '融资需求', name: 'funding_needs', type: 'input', required: false, options: '' },\n          { label: '商业计划书', name: 'business_plan', type: 'file', required: true, options: '' },\n          { label: '项目描述', name: 'project_description', type: 'textarea', required: true, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.formFields = templates[command];\n        this.$message.success('模板应用成功');\n      }\n    },\n    /** 预览表单 */\n    previewForm() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请先添加表单字段');\n        return;\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请至少添加一个表单字段');\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.formFields.length; i++) {\n        const field = this.formFields[i];\n        if (!field.label) {\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (['radio', 'checkbox', 'select'].includes(field.type) && !field.options) {\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\n          return;\n        }\n      }\n\n      const configData = JSON.stringify(this.formFields);\n      const updateData = {\n        activityId: this.activityId,\n        formConfig: configData\n      };\n\n      updateActivityConfig(updateData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadFormConfig();\n      });\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const typeNames = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        date: '日期',\n        file: '文件上传'\n      };\n      return typeNames[type] || '未知类型';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  max-width: 800px;\n  margin: 20px auto;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 300px;\n}\n\n.form-preview h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n  min-width: 100px;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n}\n\n.empty-form {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-form i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-fields-config {\n  min-height: 400px;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 10px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.toolbar-left, .toolbar-right {\n  display: flex;\n  gap: 10px;\n}\n\n.form-fields-list {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.form-field-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  background: #fff;\n  transition: all 0.3s;\n}\n\n.form-field-item:hover {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n.field-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #e4e7ed;\n  border-radius: 6px 6px 0 0;\n}\n\n.field-actions {\n  display: flex;\n  gap: 5px;\n}\n\n.danger-btn {\n  color: #f56c6c;\n}\n\n.field-content {\n  padding: 16px;\n}\n\n.field-item {\n  margin-bottom: 10px;\n}\n\n.field-item label {\n  display: block;\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #606266;\n  font-weight: 500;\n}\n\n.options-preview {\n  margin-top: 5px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-preview {\n  padding: 20px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.preview-header h3 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 2px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"]}]}