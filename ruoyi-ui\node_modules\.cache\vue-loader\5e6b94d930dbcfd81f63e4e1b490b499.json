{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=style&index=0&id=733e5256&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753755502821}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752653987088}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752653998445}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752653991020}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mb3JtLWNvbmZpZy1jb250YWluZXIgewogIG1heC13aWR0aDogODAwcHg7CiAgbWFyZ2luOiAyMHB4IGF1dG87CiAgYmFja2dyb3VuZDogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouY29uZmlnLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4IDI0cHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLmNvbmZpZy1oZWFkZXIgaDMgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXNpemU6IDE4cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLmNvbmZpZy1jb250ZW50IHsKICBwYWRkaW5nOiAyNHB4OwogIG1pbi1oZWlnaHQ6IDMwMHB4Owp9CgouZm9ybS1wcmV2aWV3IGg0IHsKICBtYXJnaW46IDAgMCAyMHB4IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxNnB4Owp9CgouZmllbGQtbGlzdCB7CiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmZpZWxkLWl0ZW0gewogIHBhZGRpbmc6IDEycHggMTZweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICBiYWNrZ3JvdW5kOiAjZmFmYmZjOwp9CgouZmllbGQtaXRlbTpsYXN0LWNoaWxkIHsKICBib3JkZXItYm90dG9tOiBub25lOwp9CgouZmllbGQtaW5mbyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTJweDsKfQoKLmZpZWxkLWljb24gewogIGNvbG9yOiAjNDA5ZWZmOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLmZpZWxkLWxhYmVsIHsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjMzAzMTMzOwogIG1pbi13aWR0aDogMTAwcHg7Cn0KCi5maWVsZC10eXBlIHsKICBjb2xvcjogIzkwOTM5OTsKICBmb250LXNpemU6IDEycHg7CiAgbWFyZ2luLWxlZnQ6IGF1dG87Cn0KCi5lbXB0eS1mb3JtIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNjBweCAyMHB4OwogIGNvbG9yOiAjOTA5Mzk5Owp9CgouZW1wdHktZm9ybSBpIHsKICBmb250LXNpemU6IDQ4cHg7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKICBkaXNwbGF5OiBibG9jazsKfQoKLmZvcm0tZmllbGRzLWNvbmZpZyB7CiAgbWluLWhlaWdodDogNDAwcHg7Cn0KCi5mb3JtLWZpZWxkcy10b29sYmFyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgcGFkZGluZzogMTBweDsKICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLnRvb2xiYXItbGVmdCwgLnRvb2xiYXItcmlnaHQgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxMHB4Owp9CgouZm9ybS1maWVsZHMtbGlzdCB7CiAgbWF4LWhlaWdodDogNTAwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQoKLmZvcm0tZmllbGQtaXRlbSB7CiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIHRyYW5zaXRpb246IGFsbCAwLjNzOwp9CgouZm9ybS1maWVsZC1pdGVtOmhvdmVyIHsKICBib3JkZXItY29sb3I6ICM0MDllZmY7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpOwp9CgouZmllbGQtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDEycHggMTZweDsKICBiYWNrZ3JvdW5kOiAjZmFmYmZjOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogIGJvcmRlci1yYWRpdXM6IDZweCA2cHggMCAwOwp9CgouZmllbGQtYWN0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDVweDsKfQoKLmRhbmdlci1idG4gewogIGNvbG9yOiAjZjU2YzZjOwp9CgouZmllbGQtY29udGVudCB7CiAgcGFkZGluZzogMTZweDsKfQoKLmZpZWxkLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5maWVsZC1pdGVtIGxhYmVsIHsKICBkaXNwbGF5OiBibG9jazsKICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjNjA2MjY2OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5vcHRpb25zLXByZXZpZXcgewogIG1hcmdpbi10b3A6IDVweDsKfQoKLmVtcHR5LXN0YXRlIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNjBweCAyMHB4OwogIGNvbG9yOiAjOTA5Mzk5Owp9CgouZW1wdHktc3RhdGUgaSB7CiAgZm9udC1zaXplOiA0OHB4OwogIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgZGlzcGxheTogYmxvY2s7Cn0KCi5mb3JtLXByZXZpZXcgewogIHBhZGRpbmc6IDIwcHg7Cn0KCi5wcmV2aWV3LWhlYWRlciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgcGFkZGluZy1ib3R0b206IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0KCi5wcmV2aWV3LWhlYWRlciBoMyB7CiAgbWFyZ2luOiAwIDAgMTBweCAwOwogIGNvbG9yOiAjMzAzMTMzOwp9CgoucHJldmlldy1oZWFkZXIgcCB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnByZXZpZXctZmllbGQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5wcmV2aWV3LWxhYmVsIHsKICBkaXNwbGF5OiBibG9jazsKICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzYwNjI2NjsKfQoKLnJlcXVpcmVkIHsKICBjb2xvcjogI2Y1NmM2YzsKICBtYXJnaW4tbGVmdDogMnB4Owp9CgoucHJldmlldy1pbnB1dCB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAinBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/activity-config", "sourcesContent": ["<template>\n  <div class=\"app-container activity-config-page\">\n    <div class=\"form-config-container\">\n      <!-- 美化的页面头部 -->\n      <div class=\"page-header\">\n        <div class=\"header-content\">\n          <div class=\"header-left\">\n            <div class=\"title-section\">\n              <h2 class=\"page-title\">\n                <i class=\"el-icon-s-grid\"></i>\n                路演活动报名表单配置\n              </h2>\n              <p class=\"page-subtitle\">设计和管理路演活动的报名表单，支持多种字段类型和灵活配置</p>\n            </div>\n          </div>\n          <div class=\"header-right\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig\"\n              v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\n              class=\"config-btn\"\n              size=\"medium\"\n            >\n              <span>配置表单</span>\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 美化的内容区域 -->\n      <div class=\"config-content\" v-loading=\"loading\">\n        <el-card shadow=\"hover\" class=\"preview-card\" v-if=\"formFields.length > 0\">\n          <div slot=\"header\" class=\"card-header\">\n            <div class=\"header-left\">\n              <i class=\"el-icon-view\"></i>\n              <span class=\"header-title\">表单字段预览</span>\n              <el-tag type=\"success\" size=\"small\">{{ formFields.length }} 个字段</el-tag>\n            </div>\n            <div class=\"header-right\">\n              <el-button type=\"text\" icon=\"el-icon-refresh\" @click=\"loadFormConfig\" size=\"small\">\n                刷新\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"field-grid\">\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-card\">\n              <div class=\"field-header\">\n                <div class=\"field-icon-wrapper\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                </div>\n                <div class=\"field-info\">\n                  <div class=\"field-name\">{{ field.label }}</div>\n                  <div class=\"field-meta\">\n                    <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\" effect=\"plain\">必填</el-tag>\n                    <el-tag v-else size=\"mini\" type=\"info\" effect=\"plain\">选填</el-tag>\n                    <span class=\"field-type-tag\">{{ getFieldTypeName(field.type) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"field-order\">{{ index + 1 }}</div>\n            </div>\n          </div>\n        </el-card>\n\n        <div v-else class=\"empty-state\">\n          <div class=\"empty-content\">\n            <div class=\"empty-icon\">\n              <i class=\"el-icon-document-add\"></i>\n            </div>\n            <h3 class=\"empty-title\">暂未配置表单字段</h3>\n            <p class=\"empty-description\">点击\"配置表单\"按钮开始设计您的报名表单</p>\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig\"\n              class=\"empty-action\"\n            >\n              立即配置\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 美化的表单配置对话框 -->\n    <el-dialog\n      title=\"\"\n      :visible.sync=\"formConfigOpen\"\n      width=\"1200px\"\n      append-to-body\n      class=\"form-config-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <!-- 对话框头部 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <div class=\"dialog-title\">\n          <i class=\"el-icon-s-grid\"></i>\n          <span>报名表单配置</span>\n        </div>\n        <div class=\"dialog-subtitle\">设计您的路演报名表单字段</div>\n      </div>\n\n      <div class=\"form-fields-config\">\n        <!-- 美化的工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"medium\" @click=\"addFormField\" icon=\"el-icon-plus\" class=\"add-field-btn\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"medium\" class=\"template-dropdown\">\n              <el-button size=\"medium\" type=\"info\" plain>\n                <i class=\"el-icon-document-copy\"></i>\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\n              预览表单\n            </el-button>\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\n              保存配置\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 美化的字段配置区域 -->\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\n          <div\n            v-for=\"(field, index) in formFields\"\n            :key=\"index\"\n            class=\"field-config-card\"\n          >\n            <div class=\"field-card-header\">\n              <div class=\"field-header-left\">\n                <div class=\"field-index-badge\">{{ index + 1 }}</div>\n                <div class=\"field-icon-wrapper\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                </div>\n                <div class=\"field-title-section\">\n                  <span class=\"field-title\">{{ field.label || '未命名字段' }}</span>\n                  <div class=\"field-meta\">\n                    <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\" effect=\"plain\">必填</el-tag>\n                    <el-tag v-else size=\"mini\" type=\"info\" effect=\"plain\">选填</el-tag>\n                    <span class=\"field-type-badge\">{{ getFieldTypeName(field.type) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"field-actions\">\n                <el-button-group size=\"small\">\n                  <el-button\n                    type=\"primary\"\n                    plain\n                    size=\"mini\"\n                    @click=\"moveField(index, -1)\"\n                    :disabled=\"index === 0\"\n                    icon=\"el-icon-arrow-up\"\n                    title=\"上移\"\n                  ></el-button>\n                  <el-button\n                    type=\"primary\"\n                    plain\n                    size=\"mini\"\n                    @click=\"moveField(index, 1)\"\n                    :disabled=\"index === formFields.length - 1\"\n                    icon=\"el-icon-arrow-down\"\n                    title=\"下移\"\n                  ></el-button>\n                </el-button-group>\n                <el-button\n                  type=\"danger\"\n                  plain\n                  size=\"mini\"\n                  @click=\"removeFormField(index)\"\n                  icon=\"el-icon-delete\"\n                  class=\"delete-btn\"\n                  title=\"删除字段\"\n                ></el-button>\n              </div>\n            </div>\n\n            <div class=\"field-content\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段标签</label>\n                    <el-input\n                      v-model=\"field.label\"\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\n                      size=\"small\"\n                      @input=\"updateFieldName(field, $event)\"\n                    />\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段类型</label>\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\n                      <el-option label=\"📞 电话\" value=\"tel\" />\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                      <el-option label=\"📅 日期\" value=\"date\" />\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\n                    </el-select>\n                  </div>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\n                <el-col :span=\"4\">\n                  <div class=\"field-item\">\n                    <label>是否必填</label>\n                    <el-switch v-model=\"field.required\" />\n                  </div>\n                </el-col>\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\n                  <div class=\"field-item\">\n                    <label>选项配置</label>\n                    <el-input\n                      v-model=\"field.options\"\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\n                      size=\"small\"\n                    />\n                    <div class=\"options-preview\" v-if=\"field.options\">\n                      <el-tag\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\n                        :key=\"optIndex\"\n                        size=\"mini\"\n                        style=\"margin-right: 5px; margin-top: 5px;\"\n                      >\n                        {{ option.trim() }}\n                      </el-tag>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div class=\"empty-state\" v-else>\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <el-input\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input-number\n                v-else-if=\"field.type === 'number'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-radio>\n              </el-radio-group>\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-checkbox>\n              </el-checkbox-group>\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\n                <el-option\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                  :value=\"option.trim()\"\n                />\n              </el-select>\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :disabled=\"true\"\n                :show-file-list=\"false\"\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>\n                  <i class=\"el-icon-upload\"></i> 选择文件\n                </el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\n              </el-upload>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-radio>\n                  </div>\n                </el-radio-group>\n              </div>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-checkbox>\n                  </div>\n                </el-checkbox-group>\n              </div>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\n                  size=\"small\"\n                  disabled\n                  style=\"width: 100%;\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\n\nexport default {\n  name: \"XiqingActivityConfig\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 表单字段配置\n      formFields: [],\n      // 活动ID（固定为1，因为只有一个路演活动配置）\n      activityId: 1\n    };\n  },\n  created() {\n    this.loadFormConfig();\n  },\n  methods: {\n    /** 加载表单配置 */\n    loadFormConfig() {\n      this.loading = true;\n      getActivityConfig(this.activityId).then(response => {\n        if (response.data && response.data.formConfig) {\n          try {\n            this.formFields = JSON.parse(response.data.formConfig);\n          } catch (e) {\n            this.formFields = [];\n          }\n        } else {\n          this.formFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.formFields = [];\n        this.loading = false;\n      });\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig() {\n      this.formConfigOpen = true;\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      this.formFields.push({\n        name: '',\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.formFields.splice(index, 1);\n    },\n    /** 移动字段位置 */\n    moveField(index, direction) {\n      const newIndex = index + direction;\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\n        const temp = this.formFields[index];\n        this.$set(this.formFields, index, this.formFields[newIndex]);\n        this.$set(this.formFields, newIndex, temp);\n      }\n    },\n    /** 更新字段名称 */\n    updateFieldName(field, label) {\n      if (!field.name || field.name === '') {\n        field.name = this.generateFieldName(label);\n      }\n    },\n    /** 生成字段名称 */\n    generateFieldName(label) {\n      const pinyin = {\n        '姓名': 'name',\n        '联系电话': 'phone',\n        '电话': 'phone',\n        '邮箱': 'email',\n        '邮箱地址': 'email',\n        '公司': 'company',\n        '项目名称': 'project_name',\n        '项目描述': 'project_description',\n        '团队规模': 'team_size'\n      };\n      return pinyin[label] || label.toLowerCase().replace(/\\s+/g, '_');\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-success',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus',\n        checkbox_other: 'el-icon-square-plus',\n        select_other: 'el-icon-plus',\n        date: 'el-icon-date',\n        file: 'el-icon-upload'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$confirm('确定要清空所有字段吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.formFields = [];\n          this.$message.success('已清空所有字段');\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }\n        ],\n        roadshow: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },\n          { label: '公司/团队', name: 'company', type: 'input', required: true, options: '' },\n          { label: '项目名称', name: 'project_name', type: 'input', required: true, options: '' },\n          { label: '项目来源', name: 'project_source', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\n          { label: '项目阶段', name: 'project_stage', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\n          { label: '团队规模', name: 'team_size', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '融资需求', name: 'funding_needs', type: 'input', required: false, options: '' },\n          { label: '商业计划书', name: 'business_plan', type: 'file', required: true, options: '' },\n          { label: '项目描述', name: 'project_description', type: 'textarea', required: true, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.formFields = templates[command];\n        this.$message.success('模板应用成功');\n      }\n    },\n    /** 预览表单 */\n    previewForm() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请先添加表单字段');\n        return;\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请至少添加一个表单字段');\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.formFields.length; i++) {\n        const field = this.formFields[i];\n        if (!field.label) {\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\n          return;\n        }\n      }\n\n      const configData = JSON.stringify(this.formFields);\n      const updateData = {\n        activityId: this.activityId,\n        formConfig: configData\n      };\n\n      updateActivityConfig(updateData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadFormConfig();\n      });\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const typeNames = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        file: '文件上传'\n      };\n      return typeNames[type] || '未知类型';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  max-width: 800px;\n  margin: 20px auto;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 300px;\n}\n\n.form-preview h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n  min-width: 100px;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n}\n\n.empty-form {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-form i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-fields-config {\n  min-height: 400px;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 10px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.toolbar-left, .toolbar-right {\n  display: flex;\n  gap: 10px;\n}\n\n.form-fields-list {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.form-field-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  background: #fff;\n  transition: all 0.3s;\n}\n\n.form-field-item:hover {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n.field-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #e4e7ed;\n  border-radius: 6px 6px 0 0;\n}\n\n.field-actions {\n  display: flex;\n  gap: 5px;\n}\n\n.danger-btn {\n  color: #f56c6c;\n}\n\n.field-content {\n  padding: 16px;\n}\n\n.field-item {\n  margin-bottom: 10px;\n}\n\n.field-item label {\n  display: block;\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #606266;\n  font-weight: 500;\n}\n\n.options-preview {\n  margin-top: 5px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-preview {\n  padding: 20px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.preview-header h3 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 2px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"]}]}