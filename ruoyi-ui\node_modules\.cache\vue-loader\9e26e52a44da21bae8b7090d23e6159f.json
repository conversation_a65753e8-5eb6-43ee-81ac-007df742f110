{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=template&id=58dd3dcc&scoped=true", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753759379338}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752653998535}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}