package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.XiqingRoadshowActivity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 西青金种子路演活动配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Mapper
public interface XiqingRoadshowActivityMapper 
{
    /**
     * 查询西青金种子路演活动配置
     * 
     * @param activityId 西青金种子路演活动配置主键
     * @return 西青金种子路演活动配置
     */
    public XiqingRoadshowActivity selectXiqingRoadshowActivityByActivityId(Long activityId);

    /**
     * 查询西青金种子路演活动配置列表
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 西青金种子路演活动配置集合
     */
    public List<XiqingRoadshowActivity> selectXiqingRoadshowActivityList(XiqingRoadshowActivity xiqingRoadshowActivity);

    /**
     * 新增西青金种子路演活动配置
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 结果
     */
    public int insertXiqingRoadshowActivity(XiqingRoadshowActivity xiqingRoadshowActivity);

    /**
     * 修改西青金种子路演活动配置
     * 
     * @param xiqingRoadshowActivity 西青金种子路演活动配置
     * @return 结果
     */
    public int updateXiqingRoadshowActivity(XiqingRoadshowActivity xiqingRoadshowActivity);

    /**
     * 删除西青金种子路演活动配置
     * 
     * @param activityId 西青金种子路演活动配置主键
     * @return 结果
     */
    public int deleteXiqingRoadshowActivityByActivityId(Long activityId);

    /**
     * 批量删除西青金种子路演活动配置
     * 
     * @param activityIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXiqingRoadshowActivityByActivityIds(Long[] activityIds);

    /**
     * 查询启用的路演活动列表（小程序端）
     *
     * @return 西青金种子路演活动配置集合
     */
    public List<XiqingRoadshowActivity> selectEnabledXiqingRoadshowActivityList();

    /**
     * 增加活动报名人数
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public int incrementParticipantCount(Long activityId);
}
