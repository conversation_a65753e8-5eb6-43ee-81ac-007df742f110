<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniDemandCategoryMapper">
    
    <resultMap type="MiniDemandCategory" id="MiniDemandCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="categoryShortName"    column="category_short_name"    />
        <result property="categoryIcon"    column="category_icon"    />
        <result property="categoryDesc"    column="category_desc"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="formFields"    column="form_fields"    />
    </resultMap>

    <sql id="selectMiniDemandCategoryVo">
        select category_id, category_name, category_code, category_short_name, category_icon, category_desc, sort_order, status, create_by, create_time, update_by, update_time, remark, form_fields from mini_demand_category
    </sql>

    <select id="selectMiniDemandCategoryList" parameterType="MiniDemandCategory" resultMap="MiniDemandCategoryResult">
        <include refid="selectMiniDemandCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniDemandCategoryByCategoryId" parameterType="Long" resultMap="MiniDemandCategoryResult">
        <include refid="selectMiniDemandCategoryVo"/>
        where category_id = #{categoryId}
    </select>

    <select id="selectEnabledMiniDemandCategoryList" resultMap="MiniDemandCategoryResult">
        <include refid="selectMiniDemandCategoryVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniDemandCategory" parameterType="MiniDemandCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into mini_demand_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryCode != null and categoryCode != ''">category_code,</if>
            <if test="categoryShortName != null and categoryShortName != ''">category_short_name,</if>
            <if test="categoryIcon != null and categoryIcon != ''">category_icon,</if>
            <if test="categoryDesc != null and categoryDesc != ''">category_desc,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="formFields != null">form_fields,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">#{categoryCode},</if>
            <if test="categoryShortName != null and categoryShortName != ''">#{categoryShortName},</if>
            <if test="categoryIcon != null and categoryIcon != ''">#{categoryIcon},</if>
            <if test="categoryDesc != null and categoryDesc != ''">#{categoryDesc},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="formFields != null">#{formFields},</if>
         </trim>
    </insert>

    <update id="updateMiniDemandCategory" parameterType="MiniDemandCategory">
        update mini_demand_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">category_code = #{categoryCode},</if>
            <if test="categoryShortName != null and categoryShortName != ''">category_short_name = #{categoryShortName},</if>
            <if test="categoryIcon != null">category_icon = #{categoryIcon},</if>
            <if test="categoryDesc != null and categoryDesc != ''">category_desc = #{categoryDesc},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="formFields != null">form_fields = #{formFields},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteMiniDemandCategoryByCategoryId" parameterType="Long">
        delete from mini_demand_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteMiniDemandCategoryByCategoryIds" parameterType="String">
        delete from mini_demand_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

</mapper> 