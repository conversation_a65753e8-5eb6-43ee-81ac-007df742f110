{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=style&index=2&id=71514f24&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753759379321}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752653987088}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752653998445}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752653991020}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5keW5hbWljLWZpZWxkcy1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTZlNmU2Ow0KfQ0KDQoubW9kdWxlLWdyb3VwIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLm1vZHVsZS10aXRsZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDllZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoubW9kdWxlLXRpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi8qIOWIhuexu+e7hOagt+W8jyAqLw0KLmNhdGVnb3J5LWdyb3VwIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmNhdGVnb3J5LXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOWVmZjsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5jYXRlZ29yeS10aXRsZSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGNvbG9yOiAjNDA5ZWZmOw0KfQ0KDQouY2F0ZWdvcnktZGVzY3JpcHRpb24gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBjb2xvcjogIzY2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KfQ0KDQovKiDliqjmgIHlrZfmrrXlnoLnm7TluIPlsYDmoLflvI8gKi8NCi5keW5hbWljLWZpZWxkLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmR5bmFtaWMtZmllbGQtaXRlbTpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsgLyog5L+d5oyB5bqV6YOo6Ze06Led77yM6YG/5YWN5LiO5LiL5pa55YWD57Sg6YeN5ZCIICovDQp9DQoNCi8qIOWtl+auteagh+etvuagt+W8jyAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtbGFiZWwgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOW/heWhq+Wtl+auteagh+ivhiAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAucmVxdWlyZWQtbWFyayB7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCn0NCg0KLyog5a2X5q615YaF5a655Yy65Z+fICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi8qIOihqOWNleaOp+S7tuagt+W8jyAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtaW5wdXQsDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC10ZXh0YXJlYSwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLXNlbGVjdCwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWlucHV0LW51bWJlciwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWRhdGUtZWRpdG9yLA0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtdGltZS1waWNrZXIgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog5Y2V6YCJ5qGG5ZKM5aSa6YCJ5qGG5biD5bGAICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC1yYWRpby1ncm91cCwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWNoZWNrYm94LWdyb3VwIHsNCiAgd2lkdGg6IDEwMCU7DQogIGxpbmUtaGVpZ2h0OiAxLjg7DQp9DQoNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLXJhZGlvLA0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtY2hlY2tib3ggew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCn0NCg0KLyog5paH5Lu25LiK5Lyg57uE5Lu2ICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC11cGxvYWQgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWRpdmlkZXIgew0KICBtYXJnaW46IDEwcHggMCAyMHB4IDA7DQp9DQoNCi8qIOWTjeW6lOW8j+W4g+WxgOS8mOWMliAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5keW5hbWljLWZpZWxkLWl0ZW0gLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICAgIHdpZHRoOiAxMDBweCAhaW1wb3J0YW50Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQogIH0NCg0KICAuZHluYW1pYy1maWVsZC1pdGVtIC5lbC1yYWRpbywNCiAgLmR5bmFtaWMtZmllbGQtaXRlbSAuZWwtY2hlY2tib3ggew0KICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIH0NCn0NCg0KLyog5LiK5Lyg57uE5Lu25qC35byP5LyY5YyWICovDQouZWwtdXBsb2FkX190aXAgew0KICBjb2xvcjogIzk5OTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tdG9wOiA1cHg7DQp9DQoNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmVsLXVwbG9hZCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZHluYW1pYy1maWVsZC1pdGVtIC5lbC11cGxvYWQtbGlzdCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoNCi8qIOaWh+S7tuS4iuS8oOebuOWFs+agt+W8jyAqLw0KLnVwbG9hZGVkLWZpbGVzLWxpc3Qgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi51cGxvYWRlZC1maWxlcy10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA1cHggMDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW0gaSB7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouZmlsZS1saW5rIHsNCiAgZmxleDogMTsNCiAgY29sb3I6ICM0MDllZmY7DQogIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5maWxlLWxpbms6aG92ZXIgew0KICBjb2xvcjogIzY2YjFmZjsNCiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7DQp9DQoNCi5yZW1vdmUtZmlsZS1idG4gew0KICBtYXJnaW4tbGVmdDogMTBweDsNCiAgY29sb3I6ICNmNTZjNmM7DQp9DQoNCi5yZW1vdmUtZmlsZS1idG46aG92ZXIgew0KICBjb2xvcjogI2Y3ODk4OTsNCn0NCg0KLyog6Z2Z5oCB5YaF5a655qC35byPICovDQouc3RhdGljLWNvbnRlbnQgew0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KfQ0KDQovKiDlt7LlrZjlnKjmlofku7bmmL7npLrmoLflvI8gKi8NCi5leGlzdGluZy1maWxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmZpbGUtZGlzcGxheSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KICBib3JkZXI6IDFweCBzb2xpZCAjYjNkOGZmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGdhcDogOHB4Ow0KfQ0KDQouZmlsZS1kaXNwbGF5IC5lbC1pY29uLWRvY3VtZW50IHsNCiAgY29sb3I6ICM0MDllZmY7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmZpbGUtZGlzcGxheSAuZmlsZS1saW5rIHsNCiAgZmxleDogMTsNCiAgY29sb3I6ICM0MDllZmY7DQogIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZmlsZS1kaXNwbGF5IC5maWxlLWxpbms6aG92ZXIgew0KICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsNCn0NCg0KLmZpbGUtZGlzcGxheSAucmVtb3ZlLWZpbGUtYnRuIHsNCiAgY29sb3I6ICNmNTZjNmM7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi8qIOWKqOaAgeWtl+auteaVtOS9k+W4g+WxgOS8mOWMliAqLw0KLmR5bmFtaWMtZmllbGRzLXNlY3Rpb24gLmVsLXJvdyB7DQogIG1hcmdpbi1sZWZ0OiAtMTBweDsNCiAgbWFyZ2luLXJpZ2h0OiAtMTBweDsNCn0NCg0KLmR5bmFtaWMtZmllbGRzLXNlY3Rpb24gLmVsLWNvbCB7DQogIHBhZGRpbmctbGVmdDogMTBweDsNCiAgcGFkZGluZy1yaWdodDogMTBweDsNCn0NCg0KLyog5LyY5YyW6KGo5Y2V6aqM6K+B6ZSZ6K+v5o+Q56S655qE5pi+56S6ICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5lbC1mb3JtLWl0ZW1fX2Vycm9yIHsNCiAgcG9zaXRpb246IHN0YXRpYzsNCiAgbWFyZ2luLXRvcDogMnB4Ow0KICBwYWRkaW5nLXRvcDogMnB4Ow0KfQ0KDQovKiDlr7nmjqXmg4XlhrXmmL7npLrmoLflvI8gKi8NCi5kb2NraW5nLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDRweDsNCn0NCg0KLmNvbnRhY3Qtc3RhdHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouY29udGFjdGVkIHsNCiAgY29sb3I6ICM2N2MyM2E7DQp9DQoNCi51bmNvbnRhY3RlZCB7DQogIGNvbG9yOiAjZTZhMjNjOw0KfQ0KDQovKiDlsZXlvIDlhoXlrrnmoLflvI8gKi8NCi5leHBhbmQtY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luOiAxMHB4IDA7DQp9DQoNCi5leHBhbmQtY29udGVudCBoNCB7DQogIG1hcmdpbjogMCAwIDE1cHggMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLm5vLWRvY2tpbmcgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOWvueaOpeivpuaDheihqOagvOagt+W8jyAqLw0KLmV4cGFuZC1jb250ZW50IC5lbC10YWJsZSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLmV4cGFuZC1jb250ZW50IC5lbC10YWJsZSB0aCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/business/demand", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n        <el-input\r\n          v-model=\"queryParams.demandTitle\"\r\n          placeholder=\"请输入需求标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n        <el-select v-model=\"queryParams.categoryId\" placeholder=\"请选择需求类型\" clearable>\r\n          <el-option\r\n            v-for=\"category in categoryList\"\r\n            :key=\"category.categoryId\"\r\n            :label=\"category.categoryName\"\r\n            :value=\"category.categoryId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n        <el-select v-model=\"queryParams.demandStatus\" placeholder=\"请选择需求状态\" clearable>\r\n          <el-option label=\"已发布\" value=\"0\" />\r\n          <el-option label=\"已对接\" value=\"1\" />\r\n          <el-option label=\"已下架\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"对接状态\" prop=\"hasDocking\">\r\n        <el-select v-model=\"queryParams.hasDocking\" placeholder=\"请选择对接状态\" clearable>\r\n          <el-option label=\"未对接\" value=\"0\" />\r\n          <el-option label=\"对接中\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"时间筛选\" prop=\"timeFilter\">\r\n        <el-select v-model=\"queryParams.timeFilter\" placeholder=\"请选择时间范围\" clearable>\r\n          <el-option label=\"一周内发布\" value=\"week_within\" />\r\n          <el-option label=\"发布满一周\" value=\"week_over\" />\r\n          <el-option label=\"发布满一月\" value=\"month_over\" />\r\n          <el-option label=\"发布满一年\" value=\"year_over\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:demand:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:demand:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:demand:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:demand:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"demandList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n      <el-table-column label=\"需求ID\" align=\"center\" prop=\"demandId\" width=\"70\" />\r\n      <el-table-column label=\"需求标题\" align=\"left\" prop=\"demandTitle\" min-width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"需求类型\" align=\"center\" prop=\"categoryName\" width=\"90\" />\r\n      <el-table-column label=\"需求状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n          <el-tag v-else-if=\"scope.row.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n          <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"对接状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.hasDocking === true || scope.row.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n          <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否置顶\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.isTop === '1'\" type=\"warning\" size=\"mini\">置顶</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"320\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"table-actions\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDetail(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:query']\"\r\n            >详情</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >修改</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:remove']\"\r\n            >删除</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleToggleTop(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >{{ scope.row.isTop === '1' ? '取消置顶' : '置顶' }}</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus !== '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOffShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #E6A23C;\"\r\n            >下架</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus === '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOnShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #67C23A;\"\r\n            >上架</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 联系记录弹窗 -->\r\n    <el-dialog title=\"联系记录\" :visible.sync=\"contactDialogVisible\" width=\"50%\" append-to-body>\r\n      <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactRules\" label-width=\"100px\">\r\n        <el-form-item label=\"对接用户\">\r\n          <span>{{ contactForm.userName }} ({{ contactForm.userPhone }})</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否已联系\" prop=\"isContacted\">\r\n          <el-radio-group v-model=\"contactForm.isContacted\">\r\n            <el-radio label=\"0\">未联系</el-radio>\r\n            <el-radio label=\"1\">已联系</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系结果\" prop=\"contactResult\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-select v-model=\"contactForm.contactResult\" placeholder=\"请选择联系结果\" clearable style=\"width: 100%;\">\r\n            <el-option label=\"联系成功\" value=\"联系成功\" />\r\n            <el-option label=\"无人接听\" value=\"无人接听\" />\r\n            <el-option label=\"号码错误\" value=\"号码错误\" />\r\n            <el-option label=\"拒绝沟通\" value=\"拒绝沟通\" />\r\n            <el-option label=\"稍后联系\" value=\"稍后联系\" />\r\n            <el-option label=\"已有合作\" value=\"已有合作\" />\r\n            <el-option label=\"不感兴趣\" value=\"不感兴趣\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系备注\" prop=\"contactNotes\">\r\n          <el-input\r\n            v-model=\"contactForm.contactNotes\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入联系备注，如沟通内容、后续计划等\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系时间\" prop=\"contactTime\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-date-picker\r\n            v-model=\"contactForm.contactTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择联系时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%;\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"contactDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitContactForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 需求详情弹窗 -->\r\n    <el-dialog title=\"需求详情\" :visible.sync=\"detailDialogVisible\" width=\"70%\" append-to-body>\r\n      <div class=\"detail-content\">\r\n        <!-- 内容信息 -->\r\n        <div class=\"info-section\">\r\n          <h4 class=\"section-header\">内容信息</h4>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">标题：</span>\r\n            <span class=\"info-value\">{{ detailForm.demandTitle || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">类型：</span>\r\n            <span class=\"info-value\">{{ detailForm.categoryName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n              <el-tag v-else-if=\"detailForm.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n              <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">对接状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.hasDocking === true || detailForm.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n              <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">是否置顶：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.isTop === '1'\" type=\"warning\" size=\"small\">置顶</el-tag>\r\n              <span v-else>否</span>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">浏览次数：</span>\r\n            <span class=\"info-value\">{{ detailForm.viewCount || 0 }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">发布时间：</span>\r\n            <span class=\"info-value\">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\" v-if=\"detailForm.demandDesc\">\r\n            <span class=\"info-label\">需求描述：</span>\r\n            <div class=\"info-value description-text\">{{ detailForm.demandDesc }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系人：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系电话：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactPhone || '' }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          表单数据\r\n        </el-divider>\r\n\r\n        <!-- 表单数据部分 -->\r\n        <div class=\"form-data-section\" v-if=\"detailForm.formDataList && detailForm.formDataList.length > 0\">\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item\r\n              v-for=\"(item, index) in detailForm.formDataList\"\r\n              :key=\"index\"\r\n              :label=\"item.label\"\r\n              :label-style=\"{ width: '120px', fontWeight: 'bold' }\"\r\n            >\r\n              <template v-if=\"item.type === 'textarea'\">\r\n                <div class=\"textarea-content\">{{ item.value || '未填写' }}</div>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'select' || item.type === 'radio'\">\r\n                <el-tag type=\"primary\" size=\"small\">{{ item.value || '未选择' }}</el-tag>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'checkbox'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <el-tag v-for=\"val in item.value\" :key=\"val\" type=\"primary\" size=\"small\" style=\"margin-right: 5px;\">{{ val }}</el-tag>\r\n                </div>\r\n                <span v-else>未选择</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'file'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <div v-for=\"(file, fileIndex) in item.value\" :key=\"fileIndex\" class=\"file-item\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <a :href=\"file.url || file\" target=\"_blank\" class=\"file-link\">\r\n                      {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n                <span v-else-if=\"typeof item.value === 'string' && item.value\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <a :href=\"item.value\" target=\"_blank\" class=\"file-link\">\r\n                    {{ getFileNameFromUrl(item.value) }}\r\n                  </a>\r\n                </span>\r\n                <span v-else>未上传</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'tel'\">\r\n                <span class=\"phone-number\">{{ item.value || '未填写' }}</span>\r\n              </template>\r\n              <template v-else>\r\n                <span>{{ item.value || '未填写' }}</span>\r\n              </template>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          对接记录\r\n          <el-tag size=\"small\" type=\"info\" style=\"margin-left: 8px;\">\r\n            共 {{ (detailForm.dockingList || []).length }} 条记录\r\n          </el-tag>\r\n        </el-divider>\r\n\r\n        <!-- 对接记录部分 -->\r\n        <div class=\"docking-section\">\r\n          <el-table\r\n            :data=\"detailForm.dockingList || []\"\r\n            size=\"small\"\r\n            border\r\n            :key=\"'docking-table-' + tableRefreshKey\"\r\n            class=\"docking-table\"\r\n            v-loading=\"detailForm.dockingLoading\"\r\n          >\r\n            <!-- 自定义空数据提示 -->\r\n            <template slot=\"empty\">\r\n              <div class=\"empty-data\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <p>暂无对接记录</p>\r\n              </div>\r\n            </template>\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n\r\n              <el-table-column label=\"对接用户\" width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"user-info\">\r\n                    <div class=\"user-name\">\r\n                      <i class=\"el-icon-user\"></i>\r\n                      <strong>{{ scope.row.userName || '未知用户' }}</strong>\r\n                    </div>\r\n                    <div class=\"user-phone\" v-if=\"scope.row.userPhone\">\r\n                      <i class=\"el-icon-phone\"></i>\r\n                      {{ scope.row.userPhone }}\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"工作信息\" min-width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"work-info\">\r\n                    <div class=\"company\" v-if=\"scope.row.userCompany\">\r\n                      <i class=\"el-icon-office-building\"></i>\r\n                      {{ scope.row.userCompany }}\r\n                    </div>\r\n                    <div class=\"position\" v-if=\"scope.row.userPosition\">\r\n                      <i class=\"el-icon-suitcase\"></i>\r\n                      {{ scope.row.userPosition }}\r\n                    </div>\r\n                    <div v-if=\"!scope.row.userCompany && !scope.row.userPosition\" class=\"no-info\">\r\n                      暂无工作信息\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"对接时间\" width=\"140\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"docking-time\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    {{ parseTime(scope.row.dockingTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"联系状态\" width=\"100\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag\r\n                    :type=\"(scope.row.isContacted === '1' || scope.row.isContacted === 1) ? 'success' : 'warning'\"\r\n                    size=\"small\"\r\n                  >\r\n                    {{ (scope.row.isContacted === '1' || scope.row.isContacted === 1) ? '已联系' : '未联系' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-edit-outline\"\r\n                    @click=\"handleContactRecord(scope.row)\"\r\n                  >\r\n                    联系记录\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改需求对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n              <el-select v-model=\"form.categoryId\" placeholder=\"请选择需求类型\" style=\"width: 100%\" @change=\"onCategoryChange\">\r\n                <el-option\r\n                  v-for=\"category in categoryList\"\r\n                  :key=\"category.categoryId\"\r\n                  :label=\"category.categoryName\"\r\n                  :value=\"category.categoryId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n              <el-select v-model=\"form.demandStatus\" placeholder=\"请选择需求状态\" style=\"width: 100%\">\r\n                <el-option label=\"已发布\" value=\"0\" />\r\n                <el-option label=\"已对接\" value=\"1\" />\r\n                <el-option label=\"已下架\" value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n          <el-input v-model=\"form.demandTitle\" placeholder=\"请输入需求标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"需求描述\" prop=\"demandDesc\">\r\n          <el-input v-model=\"form.demandDesc\" type=\"textarea\" placeholder=\"请输入需求描述\" :rows=\"4\" />\r\n        </el-form-item>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人姓名\" prop=\"contactName\">\r\n              <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人姓名\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人电话\" prop=\"contactPhone\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入联系人电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否置顶\" prop=\"isTop\">\r\n              <el-radio-group v-model=\"form.isTop\">\r\n                <el-radio label=\"0\">否</el-radio>\r\n                <el-radio label=\"1\">是</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 动态表单字段 -->\r\n        <div v-if=\"categoryFieldsData && categoryFieldsData.length > 0\" class=\"dynamic-fields-section\">\r\n          <el-divider content-position=\"left\">\r\n            <span style=\"color: #409EFF; font-weight: bold;\">{{ getCategoryName() }}专属字段</span>\r\n          </el-divider>\r\n\r\n          <!-- 渲染分类字段 -->\r\n          <div v-for=\"(categoryData, categoryIndex) in categoryFieldsData\" :key=\"`category-${categoryIndex}`\" class=\"category-group\">\r\n            <div v-if=\"categoryData.name\" class=\"category-title\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n              <span>{{ categoryData.name }}</span>\r\n            </div>\r\n            <div v-if=\"categoryData.description\" class=\"category-description\">\r\n              {{ categoryData.description }}\r\n            </div>\r\n\r\n            <div v-for=\"(field, fieldIndex) in categoryData.fields\" :key=\"`field-${field.name}-${fieldIndex}`\" class=\"dynamic-field-item\">\r\n              <div class=\"field-label\">\r\n                <span v-if=\"field.required\" class=\"required-mark\">*</span>\r\n                {{ field.label }}\r\n              </div>\r\n              <div class=\"field-content\">\r\n                <!-- 静态内容 -->\r\n                <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                  {{ field.staticContent }}\r\n                </div>\r\n                <!-- 文本输入 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'input'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 多行文本 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'textarea'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  type=\"textarea\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  :rows=\"3\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 数字输入 -->\r\n                <el-input-number\r\n                  v-else-if=\"field.type === 'number'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || 0\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 电话号码 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'tel'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 邮箱地址 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'email'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 单选框 -->\r\n                <el-radio-group\r\n                  v-else-if=\"field.type === 'radio'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-radio\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-radio-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                </el-radio-group>\r\n                <!-- 多选框 -->\r\n                <el-checkbox-group\r\n                  v-else-if=\"field.type === 'checkbox'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || []\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-checkbox\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-checkbox-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                </el-checkbox-group>\r\n                <!-- 下拉选择 -->\r\n                <el-select\r\n                  v-else-if=\"field.type === 'select'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-option-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                </el-select>\r\n                <!-- 日期选择 -->\r\n                <el-date-picker\r\n                  v-else-if=\"field.type === 'date'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  type=\"date\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 时间选择 -->\r\n                <el-time-picker\r\n                  v-else-if=\"field.type === 'time'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 文件上传 -->\r\n                <div v-else-if=\"field.type === 'file'\">\r\n                  <!-- 如果已有文件URL，显示文件信息 -->\r\n                  <div v-if=\"field.value && typeof field.value === 'string' && field.value.startsWith('http')\" class=\"existing-file\">\r\n                    <div class=\"file-display\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a :href=\"field.value\" target=\"_blank\" class=\"file-link\">\r\n                        {{ getFileNameFromUrl(field.value) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeFileUrl(field)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 文件上传组件 -->\r\n                  <el-upload\r\n                    v-else\r\n                    action=\"/dev-api/common/upload\"\r\n                    :headers=\"uploadHeaders\"\r\n                    :on-success=\"(response, file, fileList) => handleFileSuccess(response, file, fileList, field)\"\r\n                    :on-remove=\"(file, fileList) => handleFileRemove(file, fileList, field)\"\r\n                    :file-list=\"getFileList(field)\"\r\n                    :on-preview=\"handleFilePreview\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n                  </el-upload>\r\n\r\n                  <!-- 已上传文件列表显示（数组格式） -->\r\n                  <div v-if=\"Array.isArray(field.value) && field.value.length > 0\" class=\"uploaded-files-list\">\r\n                    <div class=\"uploaded-files-title\">已上传文件：</div>\r\n                    <div\r\n                      v-for=\"(file, index) in field.value\"\r\n                      :key=\"`uploaded-${field.name}-${index}`\"\r\n                      class=\"uploaded-file-item\"\r\n                    >\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a\r\n                        :href=\"file.url || file\"\r\n                        target=\"_blank\"\r\n                        class=\"file-link\"\r\n                        @click=\"downloadFile(file.url || file, file.name || getFileNameFromUrl(file))\"\r\n                      >\r\n                        {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeUploadedFile(field, index)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n/* 详情页面样式 */\r\n.detail-content {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.info-section, .form-data-section, .docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  color: #303133;\r\n}\r\n\r\n.description-text {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.6;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-link {\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n.file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-section .el-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n/* 空数据提示样式 */\r\n.empty-data {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  color: #C0C4CC;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-section .user-name {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.docking-section .user-phone {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n  margin-right: 5px;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 表格操作列样式优化 */\r\n.table-actions {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n  min-width: auto;\r\n}\r\n\r\n.table-actions .el-button + .el-button {\r\n  margin-left: 2px;\r\n}\r\n\r\n.el-table .small-padding .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n  white-space: nowrap;\r\n  overflow: visible;\r\n}\r\n\r\n.el-table .fixed-width .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n}\r\n\r\n/* 表格列间距优化 */\r\n.el-table th,\r\n.el-table td {\r\n  padding: 6px 0;\r\n}\r\n\r\n.el-table .cell {\r\n  padding-left: 6px;\r\n  padding-right: 6px;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n.docking-table .user-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .user-name {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-name i {\r\n  margin-right: 4px;\r\n  color: #409EFF;\r\n}\r\n\r\n.docking-table .user-phone {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-phone i {\r\n  margin-right: 4px;\r\n  color: #67C23A;\r\n}\r\n\r\n.docking-table .work-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .company,\r\n.docking-table .position {\r\n  margin-bottom: 4px;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .company i,\r\n.docking-table .position i {\r\n  margin-right: 4px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-table .no-info {\r\n  color: #C0C4CC;\r\n  font-size: 12px;\r\n  font-style: italic;\r\n}\r\n\r\n.docking-table .docking-time {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.docking-table .docking-time i {\r\n  margin-right: 4px;\r\n  color: #E6A23C;\r\n}\r\n\r\n/* 表格行样式 */\r\n.docking-table .el-table__row {\r\n  cursor: default;\r\n}\r\n\r\n.docking-table .el-table__row:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listDemand, getDemand, delDemand, addDemand, updateDemand, offShelfDemand, onShelfDemand, updateContactStatus } from \"@/api/miniapp/demand\";\r\nimport { getEnabledDemandCategoryList } from \"@/api/miniapp/demandcategory\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"MiniDemand\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 需求表格数据\r\n      demandList: [],\r\n      // 联系记录弹窗\r\n      contactDialogVisible: false,\r\n      // 联系记录表单\r\n      contactForm: {\r\n        dockingId: null,\r\n        userName: '',\r\n        userPhone: '',\r\n        isContacted: '0',\r\n        contactResult: '',\r\n        contactNotes: '',\r\n        contactTime: ''\r\n      },\r\n      // 联系记录表单验证\r\n      contactRules: {\r\n        isContacted: [\r\n          { required: true, message: \"请选择是否已联系\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 详情弹窗\r\n      detailDialogVisible: false,\r\n      // 详情数据\r\n      detailForm: {\r\n        dockingList: [],\r\n        formDataList: []\r\n      },\r\n      // 表格刷新key\r\n      tableRefreshKey: 0,\r\n      // 需求类型列表\r\n      categoryList: [],\r\n      // 动态表单字段\r\n      dynamicFields: [],\r\n      // 选中的类型名称\r\n      selectedCategoryName: '',\r\n      // 分类字段数据（新格式）\r\n      categoryFieldsData: [],\r\n      // 上传请求头\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + this.$store.getters.token\r\n      },\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        demandTitle: null,\r\n        categoryId: null,\r\n        demandStatus: null,\r\n        hasDocking: null,\r\n        timeFilter: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        categoryId: [\r\n          { required: true, message: \"需求类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" }\r\n        ],\r\n        demandDesc: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactName: [\r\n          { required: true, message: \"联系人姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系人电话不能为空\", trigger: \"blur\" },\r\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        demandStatus: [\r\n          { required: true, message: \"需求状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    /** 按模块分组的动态字段 */\r\n    groupedDynamicFields() {\r\n      const grouped = {};\r\n      this.dynamicFields.forEach(field => {\r\n        const moduleTitle = field.moduleTitle || '其他字段';\r\n        if (!grouped[moduleTitle]) {\r\n          grouped[moduleTitle] = [];\r\n        }\r\n        grouped[moduleTitle].push(field);\r\n      });\r\n      return grouped;\r\n    },\r\n\r\n    /** 安全的动态数据访问器 */\r\n    safeDynamicData() {\r\n      const safeData = { ...this.form.dynamicData };\r\n      this.dynamicFields.forEach(field => {\r\n        if (field.name) {\r\n          if (field.type === 'checkbox' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          } else if (field.type === 'file' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          }\r\n        }\r\n      });\r\n      return safeData;\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCategoryList();\r\n    // 测试新的数据格式\r\n    this.testNewDataFormat();\r\n  },\r\n  methods: {\r\n    /** 查询需求列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDemand(this.queryParams).then(response => {\r\n        this.demandList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取需求列表失败:', error);\r\n        this.loading = false;\r\n        this.$modal.msgError(\"获取需求列表失败\");\r\n      });\r\n    },\r\n    /** 获取需求类型列表 */\r\n    getCategoryList() {\r\n      getEnabledDemandCategoryList().then(response => {\r\n        this.categoryList = response.data;\r\n      }).catch(error => {\r\n        console.error('获取需求类型列表失败:', error);\r\n        this.$modal.msgError(\"获取需求类型列表失败\");\r\n      });\r\n    },\r\n\r\n\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        demandId: null,\r\n        categoryId: null,\r\n        demandTitle: \"\",\r\n        demandDesc: \"\",\r\n        contactName: \"\",\r\n        contactPhone: \"\",\r\n        demandStatus: \"0\",\r\n        isTop: \"0\",\r\n        remark: \"\",\r\n        dynamicData: {}\r\n      };\r\n\r\n      // 清除动态字段的验证规则\r\n      Object.keys(this.rules).forEach(key => {\r\n        if (key.startsWith('dynamicData.')) {\r\n          this.$delete(this.rules, key);\r\n        }\r\n      });\r\n\r\n      // 重置动态字段\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      this.resetForm(\"form\");\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.demandId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加需求\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // 先清理状态，但不重置表单\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      const demandId = row.demandId || this.ids;\r\n      getDemand(demandId).then(response => {\r\n        // 使用$set来保持响应式\r\n        const data = response.data;\r\n        this.$set(this.form, 'demandId', data.demandId);\r\n        this.$set(this.form, 'categoryId', data.categoryId);\r\n        this.$set(this.form, 'demandTitle', data.demandTitle || \"\");\r\n        this.$set(this.form, 'demandDesc', data.demandDesc || \"\");\r\n        this.$set(this.form, 'contactName', data.contactName || \"\");\r\n        this.$set(this.form, 'contactPhone', data.contactPhone || \"\");\r\n        this.$set(this.form, 'demandStatus', data.demandStatus || \"0\");\r\n        this.$set(this.form, 'isTop', data.isTop || \"0\");\r\n        this.$set(this.form, 'remark', data.remark || \"\");\r\n\r\n        // 解析动态表单数据\r\n        if (data.formData) {\r\n          try {\r\n            const formData = JSON.parse(data.formData);\r\n\r\n            // 检查是否是新格式的数据（包含fields数组的对象）\r\n            if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n              // 新格式：先设置表单数据，再处理分类字段数据\r\n              this.$set(this.form, 'dynamicData', {});\r\n\r\n              // 从fields中提取数据到dynamicData\r\n              formData.forEach(categoryData => {\r\n                if (categoryData.fields) {\r\n                  categoryData.fields.forEach(field => {\r\n                    if (field.value !== undefined && field.value !== null && field.value !== '') {\r\n                      this.$set(this.form.dynamicData, field.name, field.value);\r\n                    }\r\n                  });\r\n                }\r\n              });\r\n\r\n              // 处理分类字段数据\r\n              this.processCategoryFieldsData(formData);\r\n            } else {\r\n              // 旧格式：直接使用formData作为dynamicData\r\n              this.$set(this.form, 'dynamicData', formData);\r\n              this.loadDynamicFields(this.form.categoryId);\r\n            }\r\n          } catch (e) {\r\n            console.error('解析动态表单数据失败:', e);\r\n            this.$set(this.form, 'dynamicData', {});\r\n            this.loadDynamicFields(this.form.categoryId);\r\n          }\r\n        } else {\r\n          this.$set(this.form, 'dynamicData', {});\r\n          this.loadDynamicFields(this.form.categoryId);\r\n        }\r\n\r\n        // 在下一个tick中清除表单验证状态\r\n        this.$nextTick(() => {\r\n          if (this.$refs.form) {\r\n            this.$refs.form.clearValidate();\r\n          }\r\n        });\r\n\r\n        this.open = true;\r\n        this.title = \"修改需求\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 先验证动态字段\r\n      let dynamicFieldsValid = true;\r\n      let firstErrorField = null;\r\n\r\n      // 验证新格式的分类字段数据\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        this.categoryFieldsData.forEach(categoryData => {\r\n          if (categoryData.fields) {\r\n            categoryData.fields.forEach(field => {\r\n              if (field.required && field.name && field.type !== 'static') {\r\n                const value = this.form.dynamicData[field.name];\r\n                let isEmpty = false;\r\n\r\n                if (field.type === 'checkbox' || field.type === 'file') {\r\n                  isEmpty = !Array.isArray(value) || value.length === 0;\r\n                } else {\r\n                  isEmpty = value === null || value === undefined || value === '';\r\n                }\r\n\r\n                if (isEmpty) {\r\n                  dynamicFieldsValid = false;\r\n                  if (!firstErrorField) {\r\n                    firstErrorField = field.label;\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        // 验证旧格式的动态字段\r\n        this.dynamicFields.forEach(field => {\r\n          if (field.required && field.name) {\r\n            const value = this.form.dynamicData[field.name];\r\n            let isEmpty = false;\r\n\r\n            if (field.type === 'checkbox' || field.type === 'file') {\r\n              isEmpty = !Array.isArray(value) || value.length === 0;\r\n            } else {\r\n              isEmpty = value === null || value === undefined || value === '';\r\n            }\r\n\r\n            if (isEmpty) {\r\n              dynamicFieldsValid = false;\r\n              if (!firstErrorField) {\r\n                firstErrorField = field.label;\r\n              }\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (!dynamicFieldsValid) {\r\n        this.$modal.msgError(`${firstErrorField}不能为空`);\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const formData = { ...this.form };\r\n\r\n          // 构建包含value的完整字段数据格式\r\n          if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n            // 新格式：使用分类字段数据，并更新每个字段的value\r\n            const categoryDataWithValues = this.categoryFieldsData.map(categoryData => ({\r\n              ...categoryData,\r\n              fields: categoryData.fields.map(field => ({\r\n                ...field,\r\n                value: this.form.dynamicData[field.name] || field.value || (field.type === 'checkbox' || field.type === 'file' ? [] : '')\r\n              }))\r\n            }));\r\n            formData.formData = JSON.stringify(categoryDataWithValues);\r\n          } else if (formData.dynamicData && Object.keys(formData.dynamicData).length > 0) {\r\n            // 旧格式：直接使用dynamicData\r\n            formData.formData = JSON.stringify(formData.dynamicData);\r\n          }\r\n\r\n          delete formData.dynamicData; // 删除临时字段\r\n\r\n          console.log('submitForm - formData.formData:', formData.formData);\r\n\r\n          if (this.form.demandId != null) {\r\n            updateDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 查看详情 */\r\n    handleDetail(row) {\r\n      console.log('查看详情 - 原始数据:', row);\r\n\r\n      // 使用Vue.set确保响应式\r\n      this.$set(this, 'detailForm', {\r\n        ...row,\r\n        dockingLoading: true,\r\n        dockingList: [],\r\n        formDataList: []\r\n      });\r\n\r\n      // 解析表单数据\r\n      if (row.formData) {\r\n        try {\r\n          const formData = JSON.parse(row.formData);\r\n          this.$set(this.detailForm, 'formDataList', this.parseFormDataForDisplay(formData));\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n          this.$set(this.detailForm, 'formDataList', []);\r\n        }\r\n      }\r\n\r\n      console.log('详情表单数据:', this.detailForm);\r\n\r\n      // 打开弹窗\r\n      this.detailDialogVisible = true;\r\n\r\n      // 加载对接记录\r\n      request({\r\n        url: `/miniapp/demand/${row.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 联系记录操作 */\r\n    handleContactRecord(dockingRow) {\r\n      console.log('打开联系记录弹窗，对接记录数据:', dockingRow);\r\n      this.contactForm = {\r\n        dockingId: dockingRow.dockingId,\r\n        userName: dockingRow.userName,\r\n        userPhone: dockingRow.userPhone,\r\n        isContacted: dockingRow.isContacted || '0',\r\n        contactResult: dockingRow.contactResult || '',\r\n        contactNotes: dockingRow.contactNotes || '',\r\n        contactTime: dockingRow.contactTime || ''\r\n      };\r\n      console.log('联系表单数据:', this.contactForm);\r\n      this.contactDialogVisible = true;\r\n    },\r\n\r\n    /** 提交联系记录表单 */\r\n    submitContactForm() {\r\n      this.$refs[\"contactForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 如果选择已联系但没有设置联系时间，使用当前时间\r\n          if (this.contactForm.isContacted === '1' && !this.contactForm.contactTime) {\r\n            this.contactForm.contactTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');\r\n          }\r\n\r\n          // 保存当前联系状态，用于直接更新本地数据\r\n          const dockingId = this.contactForm.dockingId;\r\n          const newIsContacted = this.contactForm.isContacted;\r\n          const newContactResult = this.contactForm.contactResult;\r\n          const newContactNotes = this.contactForm.contactNotes;\r\n          const newContactTime = this.contactForm.contactTime;\r\n\r\n          updateContactStatus(this.contactForm).then((response) => {\r\n            console.log('联系状态更新成功:', response);\r\n            this.$modal.msgSuccess(\"联系记录更新成功\");\r\n            this.contactDialogVisible = false;\r\n\r\n            // 如果详情弹窗是打开的，先直接更新本地数据，再刷新详情中的对接记录\r\n            if (this.detailDialogVisible && this.detailForm.demandId && this.detailForm.dockingList) {\r\n              console.log('开始更新本地对接记录...');\r\n\r\n              // 先直接更新本地数据，立即反映变化\r\n              const dockingItem = this.detailForm.dockingList.find(item => item.dockingId === dockingId);\r\n              console.log('找到的对接记录:', dockingItem);\r\n              console.log('要更新的联系状态:', newIsContacted);\r\n              console.log('当前dockingList:', this.detailForm.dockingList);\r\n\r\n              if (dockingItem) {\r\n                console.log('更新前的联系状态:', dockingItem.isContacted);\r\n                this.$set(dockingItem, 'isContacted', newIsContacted);\r\n                this.$set(dockingItem, 'contactResult', newContactResult);\r\n                this.$set(dockingItem, 'contactNotes', newContactNotes);\r\n                this.$set(dockingItem, 'contactTime', newContactTime);\r\n                console.log('更新后的联系状态:', dockingItem.isContacted);\r\n\r\n                // 强制刷新表格\r\n                this.tableRefreshKey++;\r\n                console.log('强制刷新表格，新key:', this.tableRefreshKey);\r\n              } else {\r\n                console.error('未找到对应的对接记录，dockingId:', dockingId);\r\n                console.error('所有对接记录的ID:', this.detailForm.dockingList.map(item => item.dockingId));\r\n              }\r\n\r\n              // 然后再从服务器刷新完整数据\r\n              console.log('开始刷新详情中的对接记录...');\r\n              this.refreshDetailDockingList();\r\n            }\r\n\r\n            // 刷新主列表以更新统计数据\r\n            this.getList();\r\n          }).catch((error) => {\r\n            console.error('联系状态更新失败:', error);\r\n            this.$modal.msgError(\"更新失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 刷新详情中的对接记录 */\r\n    refreshDetailDockingList() {\r\n      this.$set(this.detailForm, 'dockingLoading', true);\r\n      request({\r\n        url: `/miniapp/demand/${this.detailForm.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('刷新对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n          console.log('更新后的对接记录:', this.detailForm.dockingList);\r\n          // 强制刷新表格\r\n          this.tableRefreshKey++;\r\n          console.log('服务器数据刷新后，强制刷新表格，新key:', this.tableRefreshKey);\r\n          // 调试数据\r\n          this.debugDockingData();\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 解析表单数据为显示格式 */\r\n    parseFormDataForDisplay(formData) {\r\n      const displayList = [];\r\n\r\n      try {\r\n        // 检查是否是新格式的数据（包含fields数组的对象）\r\n        if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n          // 新格式：遍历所有分类和字段\r\n          formData.forEach(categoryData => {\r\n            if (categoryData.fields && Array.isArray(categoryData.fields)) {\r\n              categoryData.fields.forEach(field => {\r\n                // 跳过静态展示字段\r\n                if (field.type !== 'static' && field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                  displayList.push({\r\n                    label: field.label || field.name,\r\n                    value: field.value,\r\n                    type: field.type || 'input'\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          });\r\n        } else if (typeof formData === 'object' && formData !== null) {\r\n          // 旧格式：直接遍历对象属性\r\n          Object.keys(formData).forEach(key => {\r\n            const value = formData[key];\r\n            if (value !== undefined && value !== null && value !== '') {\r\n              displayList.push({\r\n                label: key,\r\n                value: value,\r\n                type: 'input' // 默认类型\r\n              });\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error('解析表单数据失败:', e);\r\n      }\r\n\r\n      return displayList;\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      return parts[parts.length - 1] || '未知文件';\r\n    },\r\n\r\n    /** 获取联系结果标签类型 */\r\n    getContactResultType(result) {\r\n      const typeMap = {\r\n        '联系成功': 'success',\r\n        '已有合作': 'success',\r\n        '无人接听': 'warning',\r\n        '稍后联系': 'warning',\r\n        '号码错误': 'danger',\r\n        '拒绝沟通': 'danger',\r\n        '不感兴趣': 'info',\r\n        '其他': 'info'\r\n      };\r\n      return typeMap[result] || 'info';\r\n    },\r\n\r\n    /** 调试：检查当前对接记录数据 */\r\n    debugDockingData() {\r\n      console.log('=== 调试对接记录数据 ===');\r\n      console.log('detailForm.dockingList:', this.detailForm.dockingList);\r\n      if (this.detailForm.dockingList && this.detailForm.dockingList.length > 0) {\r\n        this.detailForm.dockingList.forEach((item, index) => {\r\n          console.log(`记录${index + 1}:`, {\r\n            dockingId: item.dockingId,\r\n            userName: item.userName,\r\n            isContacted: item.isContacted,\r\n            isContactedType: typeof item.isContacted\r\n          });\r\n        });\r\n      }\r\n      console.log('tableRefreshKey:', this.tableRefreshKey);\r\n      console.log('========================');\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const demandIds = row ? [row.demandId] : this.ids;\r\n      const confirmText = row\r\n        ? `是否确认删除需求编号为\"${row.demandId}\"的数据项？`\r\n        : `是否确认删除选中的${this.ids.length}条数据项？`;\r\n\r\n      this.$modal.confirm(confirmText).then(function() {\r\n        return delDemand(demandIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/demand/export', {\r\n        ...this.queryParams\r\n      }, `需求数据_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 置顶/取消置顶 */\r\n    handleToggleTop(row) {\r\n      const text = row.isTop === \"1\" ? \"取消置顶\" : \"置顶\";\r\n      const isTop = row.isTop === \"1\" ? \"0\" : \"1\";\r\n      this.$modal.confirm('确认要\"' + text + '\"需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        const updateData = {\r\n          demandId: row.demandId,\r\n          isTop: isTop\r\n        };\r\n        return updateDemand(updateData);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下架需求 */\r\n    handleOffShelf(row) {\r\n      this.$modal.confirm('确认要下架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return offShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"下架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 上架需求 */\r\n    handleOnShelf(row) {\r\n      this.$modal.confirm('确认要上架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return onShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"上架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 需求类型变化事件 */\r\n    onCategoryChange(categoryId) {\r\n      console.log('onCategoryChange - categoryId:', categoryId);\r\n\r\n      // 清空动态表单数据\r\n      this.form.dynamicData = {};\r\n      // 清空分类字段数据\r\n      this.categoryFieldsData = [];\r\n\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      console.log('onCategoryChange - found category:', category);\r\n\r\n      if (category && category.formFields) {\r\n        try {\r\n          const formConfig = JSON.parse(category.formFields);\r\n          console.log('onCategoryChange - formConfig:', formConfig);\r\n\r\n          // 检查是否是新格式的数据（包含fields数组的对象）\r\n          if (Array.isArray(formConfig) && formConfig.length > 0 && formConfig[0].fields) {\r\n            // 新格式：使用分类字段数据\r\n            this.processCategoryFieldsData(formConfig);\r\n            console.log('onCategoryChange - using new format, categoryFieldsData:', this.categoryFieldsData);\r\n          } else {\r\n            // 旧格式：使用传统的动态字段加载\r\n            this.loadDynamicFields(categoryId);\r\n            console.log('onCategoryChange - using old format, dynamicFields:', this.dynamicFields);\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单配置失败:', e);\r\n          this.loadDynamicFields(categoryId);\r\n        }\r\n      } else {\r\n        console.log('onCategoryChange - no category or formFields found');\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n      }\r\n    },\r\n\r\n    /** 加载动态表单字段 */\r\n    loadDynamicFields(categoryId) {\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      if (category) {\r\n        this.selectedCategoryName = category.categoryName;\r\n\r\n        if (category.formFields) {\r\n          try {\r\n            const formConfig = JSON.parse(category.formFields);\r\n            this.dynamicFields = [];\r\n\r\n            // 检查是否是新的模块化结构\r\n            if (Array.isArray(formConfig) && formConfig.length > 0) {\r\n              if (formConfig[0].fields) {\r\n                // 新的模块化结构：提取所有模块中的字段\r\n                formConfig.forEach(module => {\r\n                  if (module.fields && Array.isArray(module.fields)) {\r\n                    module.fields.forEach(field => {\r\n                      // 跳过静态展示字段\r\n                      if (field.type !== 'static' && field.name) {\r\n                        this.dynamicFields.push({\r\n                          ...field,\r\n                          moduleTitle: module.name // 添加模块标题用于分组显示\r\n                        });\r\n                      }\r\n                    });\r\n                  }\r\n                });\r\n              } else {\r\n                // 旧的扁平结构：直接使用\r\n                this.dynamicFields = formConfig;\r\n              }\r\n            }\r\n\r\n            // 初始化动态数据对象和验证规则\r\n            this.dynamicFields.forEach(field => {\r\n              if (field.name) {\r\n                // 确保字段总是有正确的初始值\r\n                if (field.type === 'checkbox') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    Array.isArray(this.form.dynamicData[field.name]) ? this.form.dynamicData[field.name] : []);\r\n                } else if (field.type === 'file') {\r\n                  // 处理文件字段的数据转换\r\n                  const fileData = this.form.dynamicData[field.name];\r\n                  if (typeof fileData === 'string' && fileData.trim() !== '') {\r\n                    // 如果是字符串URL，转换为对象数组格式\r\n                    const fileName = fileData.split('/').pop() || '下载文件';\r\n                    this.$set(this.form.dynamicData, field.name, [{\r\n                      name: fileName,\r\n                      url: fileData\r\n                    }]);\r\n                  } else if (Array.isArray(fileData)) {\r\n                    // 如果已经是数组，保持不变\r\n                    this.$set(this.form.dynamicData, field.name, fileData);\r\n                  } else {\r\n                    // 其他情况设为空数组\r\n                    this.$set(this.form.dynamicData, field.name, []);\r\n                  }\r\n                } else if (field.type === 'number') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else if (field.type === 'date' || field.type === 'time') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : '');\r\n                }\r\n\r\n                // 添加动态字段的验证规则\r\n                if (field.required) {\r\n                  const ruleName = `dynamicData.${field.name}`;\r\n                  this.$set(this.rules, ruleName, [\r\n                    {\r\n                      required: true,\r\n                      message: `${field.label}不能为空`,\r\n                      trigger: field.type === 'checkbox' ? 'change' : 'blur'\r\n                    }\r\n                  ]);\r\n                }\r\n              }\r\n            });\r\n          } catch (e) {\r\n            console.error('解析表单字段配置失败:', e);\r\n            this.dynamicFields = [];\r\n          }\r\n        } else {\r\n          this.dynamicFields = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取字段选项 */\r\n    getFieldOptions(field) {\r\n      if (!field.options) return [];\r\n      return field.options.split(',').map(option => option.trim()).filter(option => option);\r\n    },\r\n\r\n    /** 文件上传成功回调 */\r\n    handleFileSuccess(response, file, fileList, field) {\r\n      console.log('handleFileSuccess - response:', response, 'file:', file, 'field:', field.name);\r\n\r\n      if (response.code === 200) {\r\n        const fileUrl = response.url || response.fileName || response.data;\r\n\r\n        // 对于文件类型字段，value直接存储URL链接，不存储文件名或对象结构\r\n        this.handleFieldInput(field, fileUrl);\r\n\r\n        console.log('handleFileSuccess - 文件上传成功，设置URL:', fileUrl);\r\n        console.log('handleFileSuccess - field.value after update:', field.value);\r\n      } else {\r\n        this.$modal.msgError(response.msg || '文件上传失败');\r\n      }\r\n    },\r\n\r\n    /** 文件删除回调 */\r\n    handleFileRemove(file, fileList, field) {\r\n      // 文件删除时，直接清空value字段\r\n      this.handleFieldInput(field, '');\r\n      console.log('handleFileRemove - 文件已删除，清空字段值');\r\n    },\r\n\r\n    /** 获取多选框的安全值 */\r\n    getCheckboxValue(fieldName) {\r\n      const value = this.form.dynamicData[fieldName];\r\n      return Array.isArray(value) ? value : [];\r\n    },\r\n\r\n    /** 更新多选框的值 */\r\n    updateCheckboxValue(fieldName, value) {\r\n      this.$set(this.form.dynamicData, fieldName, Array.isArray(value) ? value : []);\r\n    },\r\n\r\n    /** 获取文件列表（用于el-upload组件） */\r\n    getFileList(field) {\r\n      const files = field.value;\r\n      console.log('getFileList - field:', field.name, 'value:', files);\r\n\r\n      // 如果是字符串URL且不为空，转换为文件列表格式显示在upload组件中\r\n      if (typeof files === 'string' && files.trim() !== '') {\r\n        return [{\r\n          name: this.getFileNameFromUrl(files),\r\n          url: files,\r\n          uid: `${field.name}-0`,\r\n          status: 'success'\r\n        }];\r\n      }\r\n\r\n      // 如果是数组格式（兼容旧数据）\r\n      if (Array.isArray(files)) {\r\n        return files.map((file, index) => ({\r\n          name: file.name || this.getFileNameFromUrl(file.url || file),\r\n          url: file.url || file,\r\n          uid: `${field.name}-${index}`,\r\n          status: 'success'\r\n        }));\r\n      }\r\n\r\n      // 其他情况返回空数组\r\n      console.log('getFileList - 无有效文件数据，返回空数组');\r\n      return [];\r\n    },\r\n\r\n    /** 获取已上传的文件列表（用于显示） */\r\n    getUploadedFiles(field) {\r\n      const files = field.value;\r\n      return Array.isArray(files) ? files : [];\r\n    },\r\n\r\n    /** 文件预览 */\r\n    handleFilePreview(file) {\r\n      if (file.url) {\r\n        window.open(file.url, '_blank');\r\n      }\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(url, fileName) {\r\n      // 创建一个临时的a标签来触发下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 删除已上传的文件 */\r\n    removeUploadedFile(field, index) {\r\n      if (field.value && Array.isArray(field.value)) {\r\n        const newValue = [...field.value];\r\n        newValue.splice(index, 1);\r\n        this.handleFieldInput(field, newValue);\r\n      }\r\n    },\r\n\r\n    /** 删除文件URL */\r\n    removeFileUrl(field) {\r\n      this.handleFieldInput(field, '');\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      const fileName = parts[parts.length - 1];\r\n      // 如果文件名包含时间戳等，尝试提取原始文件名\r\n      const match = fileName.match(/.*_\\d+A\\d+\\.(.*)/);\r\n      if (match) {\r\n        return `文件.${match[1]}`;\r\n      }\r\n      return fileName || '未知文件';\r\n    },\r\n\r\n    /** 处理字段输入 */\r\n    handleFieldInput(field, value) {\r\n      // 更新字段的value\r\n      field.value = value;\r\n      // 同步到表单数据\r\n      this.$set(this.form.dynamicData, field.name, value);\r\n      console.log('handleFieldInput - field:', field.name, 'value:', value);\r\n    },\r\n\r\n    /** 更新字段值到表单数据 */\r\n    updateFieldValue(field) {\r\n      this.$set(this.form.dynamicData, field.name, field.value);\r\n    },\r\n\r\n    /** 获取分类名称 */\r\n    getCategoryName() {\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        return this.categoryFieldsData[0].name || '专属字段';\r\n      }\r\n      return this.selectedCategoryName || '专属字段';\r\n    },\r\n\r\n    /** 处理分类字段数据 */\r\n    processCategoryFieldsData(data) {\r\n      if (typeof data === 'string') {\r\n        try {\r\n          this.categoryFieldsData = JSON.parse(data);\r\n        } catch (e) {\r\n          console.error('解析分类字段数据失败:', e);\r\n          this.categoryFieldsData = [];\r\n        }\r\n      } else if (Array.isArray(data)) {\r\n        this.categoryFieldsData = data;\r\n      } else {\r\n        this.categoryFieldsData = [];\r\n      }\r\n\r\n      // 初始化字段值到表单数据\r\n      this.categoryFieldsData.forEach(categoryData => {\r\n        if (categoryData.fields) {\r\n          categoryData.fields.forEach(field => {\r\n            // 确保字段有初始值\r\n            if (field.value === undefined || field.value === null) {\r\n              if (field.type === 'file') {\r\n                field.value = [];\r\n              } else if (field.type === 'checkbox') {\r\n                field.value = [];\r\n              } else {\r\n                field.value = '';\r\n              }\r\n            }\r\n\r\n            // 从表单数据中恢复字段值（如果存在）\r\n            if (this.form.dynamicData && this.form.dynamicData[field.name] !== undefined) {\r\n              field.value = this.form.dynamicData[field.name];\r\n            } else {\r\n              // 设置到表单数据\r\n              this.$set(this.form.dynamicData, field.name, field.value);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 测试新的数据格式 */\r\n    testNewDataFormat() {\r\n      // 使用您提供的实际JSON数据格式进行测试\r\n      const testData = [\r\n        {\r\n          \"name\": \"基础信息\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"企业全称\",\r\n              \"name\": \"field_652408\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"测试企业有限公司\"\r\n            },\r\n            {\r\n              \"label\": \"行业标签\",\r\n              \"name\": \"field_720944\",\r\n              \"type\": \"select\",\r\n              \"required\": true,\r\n              \"options\": \"新能源,硬科技\",\r\n              \"placeholder\": \"请选择\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"新能源\"\r\n            },\r\n            {\r\n              \"label\": \"联系人\",\r\n              \"name\": \"contact_name\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"张三\"\r\n            },\r\n            {\r\n              \"label\": \"电话\",\r\n              \"name\": \"phone\",\r\n              \"type\": \"tel\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"13800138000\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"\r\n        },\r\n        {\r\n          \"name\": \"其他材料补充\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"上传附件\",\r\n              \"name\": \"field_989222\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"未选择任何文件\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"http://************:8080/profile/upload/2025/07/23/xhuFwa0qulPS03911c35329f695848fb659a24f6f159_20250723183220A001.png\"\r\n            },\r\n            {\r\n              \"label\": \"邮件提交至\",\r\n              \"name\": \"field_227969\",\r\n              \"type\": \"static\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\r\n              \"value\": \"\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"\r\n        }\r\n      ];\r\n\r\n      // 当点击修改按钮时，可以调用这个方法来设置测试数据\r\n      // this.processCategoryFieldsData(testData);\r\n    },\r\n\r\n\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n/* 详情弹窗样式 - 参考简洁布局 */\r\n.detail-content {\r\n  padding: 0;\r\n  background-color: #f5f5f5;\r\n  min-height: 400px;\r\n}\r\n\r\n.info-section {\r\n  background-color: white;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  background-color: #f8f9fa;\r\n  padding: 12px 20px;\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  color: #666;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  min-width: 100px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.info-value {\r\n  color: #333;\r\n  font-size: 14px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.description-text {\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.docking-section {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.docking-list {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.docking-item {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.docking-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-main {\r\n  flex: 1;\r\n}\r\n\r\n.item-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  gap: 10px;\r\n}\r\n\r\n.user-name {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.user-phone {\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.item-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item i {\r\n  margin-right: 4px;\r\n  color: #999;\r\n}\r\n\r\n.item-notes {\r\n  color: #666;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-notes i {\r\n  margin-right: 4px;\r\n  margin-top: 2px;\r\n  color: #999;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.item-actions {\r\n  margin-left: 15px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.dynamic-fields-section {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border: 1px solid #e6e6e6;\r\n}\r\n\r\n.module-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.module-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 分类组样式 */\r\n.category-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.category-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.category-description {\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 动态字段垂直布局样式 */\r\n.dynamic-field-item {\r\n  margin-bottom: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item:last-child {\r\n  margin-bottom: 20px; /* 保持底部间距，避免与下方元素重合 */\r\n}\r\n\r\n/* 字段标签样式 */\r\n.dynamic-field-item .field-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 必填字段标识 */\r\n.dynamic-field-item .required-mark {\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 字段内容区域 */\r\n.dynamic-field-item .field-content {\r\n  width: 100%;\r\n}\r\n\r\n/* 表单控件样式 */\r\n.dynamic-field-item .field-content .el-input,\r\n.dynamic-field-item .field-content .el-textarea,\r\n.dynamic-field-item .field-content .el-select,\r\n.dynamic-field-item .field-content .el-input-number,\r\n.dynamic-field-item .field-content .el-date-editor,\r\n.dynamic-field-item .field-content .el-time-picker {\r\n  width: 100%;\r\n}\r\n\r\n/* 单选框和多选框布局 */\r\n.dynamic-field-item .field-content .el-radio-group,\r\n.dynamic-field-item .field-content .el-checkbox-group {\r\n  width: 100%;\r\n  line-height: 1.8;\r\n}\r\n\r\n.dynamic-field-item .field-content .el-radio,\r\n.dynamic-field-item .field-content .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 文件上传组件 */\r\n.dynamic-field-item .field-content .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.el-divider {\r\n  margin: 10px 0 20px 0;\r\n}\r\n\r\n/* 响应式布局优化 */\r\n@media (max-width: 768px) {\r\n  .dynamic-field-item .el-form-item__label {\r\n    width: 100px !important;\r\n    text-align: left;\r\n  }\r\n\r\n  .dynamic-field-item .el-radio,\r\n  .dynamic-field-item .el-checkbox {\r\n    display: block;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 上传组件样式优化 */\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.dynamic-field-item .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item .el-upload-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 文件上传相关样式 */\r\n.uploaded-files-list {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 4px;\r\n}\r\n\r\n.uploaded-files-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.uploaded-file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.uploaded-file-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.uploaded-file-item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-link:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.remove-file-btn {\r\n  margin-left: 10px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.remove-file-btn:hover {\r\n  color: #f78989;\r\n}\r\n\r\n/* 静态内容样式 */\r\n.static-content {\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 已存在文件显示样式 */\r\n.existing-file {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-display {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 4px;\r\n  gap: 8px;\r\n}\r\n\r\n.file-display .el-icon-document {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-display .file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-display .file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.file-display .remove-file-btn {\r\n  color: #f56c6c;\r\n  padding: 0;\r\n}\r\n\r\n/* 动态字段整体布局优化 */\r\n.dynamic-fields-section .el-row {\r\n  margin-left: -10px;\r\n  margin-right: -10px;\r\n}\r\n\r\n.dynamic-fields-section .el-col {\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n\r\n/* 优化表单验证错误提示的显示 */\r\n.dynamic-field-item .el-form-item__error {\r\n  position: static;\r\n  margin-top: 2px;\r\n  padding-top: 2px;\r\n}\r\n\r\n/* 对接情况显示样式 */\r\n.docking-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.contact-stats {\r\n  display: flex;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-content h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-docking {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n/* 对接详情表格样式 */\r\n.expand-content .el-table {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.expand-content .el-table th {\r\n  background-color: #fafafa;\r\n}\r\n</style>\r\n"]}]}