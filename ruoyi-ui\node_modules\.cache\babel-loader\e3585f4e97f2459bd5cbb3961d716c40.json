{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753755502821}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751858756942}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752653991061}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_activityConfig", "require", "name", "data", "loading", "formConfigOpen", "previewDialogVisible", "formFields", "activityId", "created", "loadFormConfig", "methods", "_this", "getActivityConfig", "then", "response", "formConfig", "JSON", "parse", "e", "catch", "handleFormConfig", "addFormField", "push", "label", "type", "required", "options", "removeFormField", "index", "splice", "moveField", "direction", "newIndex", "length", "temp", "$set", "updateFieldName", "field", "generateFieldName", "pinyin", "toLowerCase", "replace", "getFieldIcon", "icons", "input", "textarea", "number", "email", "tel", "radio", "checkbox", "select", "radio_other", "checkbox_other", "select_other", "date", "file", "handleTemplateCommand", "command", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "$message", "success", "templates", "basic", "roadshow", "previewForm", "warning", "saveFormConfig", "_this3", "i", "error", "concat", "includes", "configData", "stringify", "updateData", "updateActivityConfig", "$modal", "msgSuccess", "getFieldTypeName", "typeNames"], "sources": ["src/views/miniapp/xiqing/activity-config/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container activity-config-page\">\n    <div class=\"form-config-container\">\n      <!-- 美化的页面头部 -->\n      <div class=\"page-header\">\n        <div class=\"header-content\">\n          <div class=\"header-left\">\n            <div class=\"title-section\">\n              <h2 class=\"page-title\">\n                <i class=\"el-icon-s-grid\"></i>\n                路演活动报名表单配置\n              </h2>\n              <p class=\"page-subtitle\">设计和管理路演活动的报名表单，支持多种字段类型和灵活配置</p>\n            </div>\n          </div>\n          <div class=\"header-right\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig\"\n              v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\n              class=\"config-btn\"\n              size=\"medium\"\n            >\n              <span>配置表单</span>\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 美化的内容区域 -->\n      <div class=\"config-content\" v-loading=\"loading\">\n        <el-card shadow=\"hover\" class=\"preview-card\" v-if=\"formFields.length > 0\">\n          <div slot=\"header\" class=\"card-header\">\n            <div class=\"header-left\">\n              <i class=\"el-icon-view\"></i>\n              <span class=\"header-title\">表单字段预览</span>\n              <el-tag type=\"success\" size=\"small\">{{ formFields.length }} 个字段</el-tag>\n            </div>\n            <div class=\"header-right\">\n              <el-button type=\"text\" icon=\"el-icon-refresh\" @click=\"loadFormConfig\" size=\"small\">\n                刷新\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"field-grid\">\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-card\">\n              <div class=\"field-header\">\n                <div class=\"field-icon-wrapper\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                </div>\n                <div class=\"field-info\">\n                  <div class=\"field-name\">{{ field.label }}</div>\n                  <div class=\"field-meta\">\n                    <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\" effect=\"plain\">必填</el-tag>\n                    <el-tag v-else size=\"mini\" type=\"info\" effect=\"plain\">选填</el-tag>\n                    <span class=\"field-type-tag\">{{ getFieldTypeName(field.type) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"field-order\">{{ index + 1 }}</div>\n            </div>\n          </div>\n        </el-card>\n\n        <div v-else class=\"empty-state\">\n          <div class=\"empty-content\">\n            <div class=\"empty-icon\">\n              <i class=\"el-icon-document-add\"></i>\n            </div>\n            <h3 class=\"empty-title\">暂未配置表单字段</h3>\n            <p class=\"empty-description\">点击\"配置表单\"按钮开始设计您的报名表单</p>\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-setting\"\n              @click=\"handleFormConfig\"\n              class=\"empty-action\"\n            >\n              立即配置\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 美化的表单配置对话框 -->\n    <el-dialog\n      title=\"\"\n      :visible.sync=\"formConfigOpen\"\n      width=\"1200px\"\n      append-to-body\n      class=\"form-config-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <!-- 对话框头部 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <div class=\"dialog-title\">\n          <i class=\"el-icon-s-grid\"></i>\n          <span>报名表单配置</span>\n        </div>\n        <div class=\"dialog-subtitle\">设计您的路演报名表单字段</div>\n      </div>\n\n      <div class=\"form-fields-config\">\n        <!-- 美化的工具栏 -->\n        <div class=\"form-fields-toolbar\">\n          <div class=\"toolbar-left\">\n            <el-button type=\"primary\" size=\"medium\" @click=\"addFormField\" icon=\"el-icon-plus\" class=\"add-field-btn\">\n              添加字段\n            </el-button>\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"medium\" class=\"template-dropdown\">\n              <el-button size=\"medium\" type=\"info\" plain>\n                <i class=\"el-icon-document-copy\"></i>\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n          <div class=\"toolbar-right\">\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\n              预览表单\n            </el-button>\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\n              保存配置\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 美化的字段配置区域 -->\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\n          <div\n            v-for=\"(field, index) in formFields\"\n            :key=\"index\"\n            class=\"field-config-card\"\n          >\n            <div class=\"field-card-header\">\n              <div class=\"field-header-left\">\n                <div class=\"field-index-badge\">{{ index + 1 }}</div>\n                <div class=\"field-icon-wrapper\">\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\n                </div>\n                <div class=\"field-title-section\">\n                  <span class=\"field-title\">{{ field.label || '未命名字段' }}</span>\n                  <div class=\"field-meta\">\n                    <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\" effect=\"plain\">必填</el-tag>\n                    <el-tag v-else size=\"mini\" type=\"info\" effect=\"plain\">选填</el-tag>\n                    <span class=\"field-type-badge\">{{ getFieldTypeName(field.type) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"field-actions\">\n                <el-button-group size=\"small\">\n                  <el-button\n                    type=\"primary\"\n                    plain\n                    size=\"mini\"\n                    @click=\"moveField(index, -1)\"\n                    :disabled=\"index === 0\"\n                    icon=\"el-icon-arrow-up\"\n                    title=\"上移\"\n                  ></el-button>\n                  <el-button\n                    type=\"primary\"\n                    plain\n                    size=\"mini\"\n                    @click=\"moveField(index, 1)\"\n                    :disabled=\"index === formFields.length - 1\"\n                    icon=\"el-icon-arrow-down\"\n                    title=\"下移\"\n                  ></el-button>\n                </el-button-group>\n                <el-button\n                  type=\"danger\"\n                  plain\n                  size=\"mini\"\n                  @click=\"removeFormField(index)\"\n                  icon=\"el-icon-delete\"\n                  class=\"delete-btn\"\n                  title=\"删除字段\"\n                ></el-button>\n              </div>\n            </div>\n\n            <div class=\"field-content\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段标签</label>\n                    <el-input\n                      v-model=\"field.label\"\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\n                      size=\"small\"\n                      @input=\"updateFieldName(field, $event)\"\n                    />\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"field-item\">\n                    <label>字段类型</label>\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\n                      <el-option label=\"📞 电话\" value=\"tel\" />\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\n                      <el-option label=\"📅 日期\" value=\"date\" />\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\n                    </el-select>\n                  </div>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\n                <el-col :span=\"4\">\n                  <div class=\"field-item\">\n                    <label>是否必填</label>\n                    <el-switch v-model=\"field.required\" />\n                  </div>\n                </el-col>\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\n                  <div class=\"field-item\">\n                    <label>选项配置</label>\n                    <el-input\n                      v-model=\"field.options\"\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\n                      size=\"small\"\n                    />\n                    <div class=\"options-preview\" v-if=\"field.options\">\n                      <el-tag\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\n                        :key=\"optIndex\"\n                        size=\"mini\"\n                        style=\"margin-right: 5px; margin-top: 5px;\"\n                      >\n                        {{ option.trim() }}\n                      </el-tag>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div class=\"empty-state\" v-else>\n          <i class=\"el-icon-document-add\"></i>\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\n      <div class=\"form-preview\">\n        <div class=\"preview-header\">\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\n          <p>请填写以下信息完成报名</p>\n        </div>\n        <div class=\"preview-form\">\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\n            <label class=\"preview-label\">\n              {{ field.label }}\n              <span v-if=\"field.required\" class=\"required\">*</span>\n            </label>\n            <div class=\"preview-input\">\n              <el-input\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input\n                v-else-if=\"field.type === 'textarea'\"\n                type=\"textarea\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n              />\n              <el-input-number\n                v-else-if=\"field.type === 'number'\"\n                :placeholder=\"'请输入' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\n                <el-radio\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-radio>\n              </el-radio-group>\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\n                <el-checkbox\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                >\n                  {{ option.trim() }}\n                </el-checkbox>\n              </el-checkbox-group>\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\n                <el-option\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\n                  :key=\"optIndex\"\n                  :label=\"option.trim()\"\n                  :value=\"option.trim()\"\n                />\n              </el-select>\n              <el-date-picker\n                v-else-if=\"field.type === 'date'\"\n                type=\"date\"\n                :placeholder=\"'请选择' + field.label\"\n                size=\"small\"\n                disabled\n                style=\"width: 100%\"\n              />\n              <el-upload\n                v-else-if=\"field.type === 'file'\"\n                class=\"upload-demo\"\n                action=\"#\"\n                :disabled=\"true\"\n                :show-file-list=\"false\"\n              >\n                <el-button size=\"small\" type=\"primary\" disabled>\n                  <i class=\"el-icon-upload\"></i> 选择文件\n                </el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\n              </el-upload>\n              <!-- 单选+其他 -->\n              <div v-else-if=\"field.type === 'radio_other'\">\n                <el-radio-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-radio label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-radio>\n                  </div>\n                </el-radio-group>\n              </div>\n              <!-- 多选+其他 -->\n              <div v-else-if=\"field.type === 'checkbox_other'\">\n                <el-checkbox-group disabled>\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\n                  </div>\n                  <div style=\"display: block; margin-bottom: 8px;\">\n                    <el-checkbox label=\"其他\">\n                      其他\n                      <el-input\n                        placeholder=\"请输入其他内容\"\n                        size=\"small\"\n                        disabled\n                        style=\"width: 200px; margin-left: 10px;\"\n                      />\n                    </el-checkbox>\n                  </div>\n                </el-checkbox-group>\n              </div>\n              <!-- 下拉+其他 -->\n              <div v-else-if=\"field.type === 'select_other'\">\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\n                  <el-option label=\"其他\" value=\"其他\" />\n                </el-select>\n                <el-input\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\n                  size=\"small\"\n                  disabled\n                  style=\"width: 100%;\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\n\nexport default {\n  name: \"XiqingActivityConfig\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 是否显示表单配置弹出层\n      formConfigOpen: false,\n      // 是否显示表单预览弹出层\n      previewDialogVisible: false,\n      // 表单字段配置\n      formFields: [],\n      // 活动ID（固定为1，因为只有一个路演活动配置）\n      activityId: 1\n    };\n  },\n  created() {\n    this.loadFormConfig();\n  },\n  methods: {\n    /** 加载表单配置 */\n    loadFormConfig() {\n      this.loading = true;\n      getActivityConfig(this.activityId).then(response => {\n        if (response.data && response.data.formConfig) {\n          try {\n            this.formFields = JSON.parse(response.data.formConfig);\n          } catch (e) {\n            this.formFields = [];\n          }\n        } else {\n          this.formFields = [];\n        }\n        this.loading = false;\n      }).catch(() => {\n        this.formFields = [];\n        this.loading = false;\n      });\n    },\n    /** 表单配置按钮操作 */\n    handleFormConfig() {\n      this.formConfigOpen = true;\n    },\n    /** 添加表单字段 */\n    addFormField() {\n      this.formFields.push({\n        name: '',\n        label: '',\n        type: 'input',\n        required: false,\n        options: ''\n      });\n    },\n    /** 删除表单字段 */\n    removeFormField(index) {\n      this.formFields.splice(index, 1);\n    },\n    /** 移动字段位置 */\n    moveField(index, direction) {\n      const newIndex = index + direction;\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\n        const temp = this.formFields[index];\n        this.$set(this.formFields, index, this.formFields[newIndex]);\n        this.$set(this.formFields, newIndex, temp);\n      }\n    },\n    /** 更新字段名称 */\n    updateFieldName(field, label) {\n      if (!field.name || field.name === '') {\n        field.name = this.generateFieldName(label);\n      }\n    },\n    /** 生成字段名称 */\n    generateFieldName(label) {\n      const pinyin = {\n        '姓名': 'name',\n        '联系电话': 'phone',\n        '电话': 'phone',\n        '邮箱': 'email',\n        '邮箱地址': 'email',\n        '公司': 'company',\n        '项目名称': 'project_name',\n        '项目描述': 'project_description',\n        '团队规模': 'team_size'\n      };\n      return pinyin[label] || label.toLowerCase().replace(/\\s+/g, '_');\n    },\n    /** 获取字段图标 */\n    getFieldIcon(type) {\n      const icons = {\n        input: 'el-icon-edit',\n        textarea: 'el-icon-document',\n        number: 'el-icon-s-data',\n        email: 'el-icon-message',\n        tel: 'el-icon-phone',\n        radio: 'el-icon-success',\n        checkbox: 'el-icon-check',\n        select: 'el-icon-arrow-down',\n        radio_other: 'el-icon-circle-plus',\n        checkbox_other: 'el-icon-square-plus',\n        select_other: 'el-icon-plus',\n        date: 'el-icon-date',\n        file: 'el-icon-upload'\n      };\n      return icons[type] || 'el-icon-edit';\n    },\n    /** 处理模板命令 */\n    handleTemplateCommand(command) {\n      if (command === 'clear') {\n        this.$confirm('确定要清空所有字段吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.formFields = [];\n          this.$message.success('已清空所有字段');\n        });\n        return;\n      }\n\n      const templates = {\n        basic: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }\n        ],\n        roadshow: [\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\n          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },\n          { label: '公司/团队', name: 'company', type: 'input', required: true, options: '' },\n          { label: '项目名称', name: 'project_name', type: 'input', required: true, options: '' },\n          { label: '项目来源', name: 'project_source', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\n          { label: '项目阶段', name: 'project_stage', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\n          { label: '团队规模', name: 'team_size', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\n          { label: '融资需求', name: 'funding_needs', type: 'input', required: false, options: '' },\n          { label: '商业计划书', name: 'business_plan', type: 'file', required: true, options: '' },\n          { label: '项目描述', name: 'project_description', type: 'textarea', required: true, options: '' }\n        ]\n      };\n\n      if (templates[command]) {\n        this.formFields = templates[command];\n        this.$message.success('模板应用成功');\n      }\n    },\n    /** 预览表单 */\n    previewForm() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请先添加表单字段');\n        return;\n      }\n      this.previewDialogVisible = true;\n    },\n    /** 保存表单配置 */\n    saveFormConfig() {\n      if (this.formFields.length === 0) {\n        this.$message.warning('请至少添加一个表单字段');\n        return;\n      }\n\n      // 验证字段配置\n      for (let i = 0; i < this.formFields.length; i++) {\n        const field = this.formFields[i];\n        if (!field.label) {\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\n          return;\n        }\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\n          return;\n        }\n      }\n\n      const configData = JSON.stringify(this.formFields);\n      const updateData = {\n        activityId: this.activityId,\n        formConfig: configData\n      };\n\n      updateActivityConfig(updateData).then(response => {\n        this.$modal.msgSuccess(\"表单配置保存成功\");\n        this.formConfigOpen = false;\n        this.loadFormConfig();\n      });\n    },\n    /** 获取字段类型名称 */\n    getFieldTypeName(type) {\n      const typeNames = {\n        input: '文本输入',\n        textarea: '多行文本',\n        number: '数字输入',\n        email: '邮箱',\n        tel: '电话',\n        radio: '单选',\n        checkbox: '多选',\n        select: '下拉选择',\n        radio_other: '单选+其他',\n        checkbox_other: '多选+其他',\n        select_other: '下拉+其他',\n        date: '日期',\n        file: '文件上传'\n      };\n      return typeNames[type] || '未知类型';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-config-container {\n  max-width: 800px;\n  margin: 20px auto;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.config-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.config-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.config-content {\n  padding: 24px;\n  min-height: 300px;\n}\n\n.form-preview h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.field-list {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.field-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.field-item:last-child {\n  border-bottom: none;\n}\n\n.field-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.field-icon {\n  color: #409eff;\n  font-size: 16px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #303133;\n  min-width: 100px;\n}\n\n.field-type {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n}\n\n.empty-form {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-form i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-fields-config {\n  min-height: 400px;\n}\n\n.form-fields-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 10px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.toolbar-left, .toolbar-right {\n  display: flex;\n  gap: 10px;\n}\n\n.form-fields-list {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.form-field-item {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  background: #fff;\n  transition: all 0.3s;\n}\n\n.form-field-item:hover {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n.field-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafbfc;\n  border-bottom: 1px solid #e4e7ed;\n  border-radius: 6px 6px 0 0;\n}\n\n.field-actions {\n  display: flex;\n  gap: 5px;\n}\n\n.danger-btn {\n  color: #f56c6c;\n}\n\n.field-content {\n  padding: 16px;\n}\n\n.field-item {\n  margin-bottom: 10px;\n}\n\n.field-item label {\n  display: block;\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #606266;\n  font-weight: 500;\n}\n\n.options-preview {\n  margin-top: 5px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.form-preview {\n  padding: 20px;\n}\n\n.preview-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.preview-header h3 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n\n.preview-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.preview-field {\n  margin-bottom: 20px;\n}\n\n.preview-label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.required {\n  color: #f56c6c;\n  margin-left: 2px;\n}\n\n.preview-input {\n  width: 100%;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AA6ZA,IAAAA,eAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,cAAA;MACA;MACAC,oBAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,iCAAA,OAAAL,UAAA,EAAAM,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAZ,IAAA,IAAAY,QAAA,CAAAZ,IAAA,CAAAa,UAAA;UACA;YACAJ,KAAA,CAAAL,UAAA,GAAAU,IAAA,CAAAC,KAAA,CAAAH,QAAA,CAAAZ,IAAA,CAAAa,UAAA;UACA,SAAAG,CAAA;YACAP,KAAA,CAAAL,UAAA;UACA;QACA;UACAK,KAAA,CAAAL,UAAA;QACA;QACAK,KAAA,CAAAR,OAAA;MACA,GAAAgB,KAAA;QACAR,KAAA,CAAAL,UAAA;QACAK,KAAA,CAAAR,OAAA;MACA;IACA;IACA,eACAiB,gBAAA,WAAAA,iBAAA;MACA,KAAAhB,cAAA;IACA;IACA,aACAiB,YAAA,WAAAA,aAAA;MACA,KAAAf,UAAA,CAAAgB,IAAA;QACArB,IAAA;QACAsB,KAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;MACA;IACA;IACA,aACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAtB,UAAA,CAAAuB,MAAA,CAAAD,KAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAF,KAAA,EAAAG,SAAA;MACA,IAAAC,QAAA,GAAAJ,KAAA,GAAAG,SAAA;MACA,IAAAC,QAAA,SAAAA,QAAA,QAAA1B,UAAA,CAAA2B,MAAA;QACA,IAAAC,IAAA,QAAA5B,UAAA,CAAAsB,KAAA;QACA,KAAAO,IAAA,MAAA7B,UAAA,EAAAsB,KAAA,OAAAtB,UAAA,CAAA0B,QAAA;QACA,KAAAG,IAAA,MAAA7B,UAAA,EAAA0B,QAAA,EAAAE,IAAA;MACA;IACA;IACA,aACAE,eAAA,WAAAA,gBAAAC,KAAA,EAAAd,KAAA;MACA,KAAAc,KAAA,CAAApC,IAAA,IAAAoC,KAAA,CAAApC,IAAA;QACAoC,KAAA,CAAApC,IAAA,QAAAqC,iBAAA,CAAAf,KAAA;MACA;IACA;IACA,aACAe,iBAAA,WAAAA,kBAAAf,KAAA;MACA,IAAAgB,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAhB,KAAA,KAAAA,KAAA,CAAAiB,WAAA,GAAAC,OAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAlB,IAAA;MACA,IAAAmB,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,OAAAb,KAAA,CAAAnB,IAAA;IACA;IACA,aACAiC,qBAAA,WAAAA,sBAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAAE,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAtC,IAAA;QACA,GAAAX,IAAA;UACA8C,MAAA,CAAArD,UAAA;UACAqD,MAAA,CAAAI,QAAA,CAAAC,OAAA;QACA;QACA;MACA;MAEA,IAAAC,SAAA;QACAC,KAAA,GACA;UAAA3C,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACAyC,QAAA,GACA;UAAA5C,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAtB,IAAA;UAAAuB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA;MAEA;MAEA,IAAAuC,SAAA,CAAAP,OAAA;QACA,KAAApD,UAAA,GAAA2D,SAAA,CAAAP,OAAA;QACA,KAAAK,QAAA,CAAAC,OAAA;MACA;IACA;IACA,WACAI,WAAA,WAAAA,YAAA;MACA,SAAA9D,UAAA,CAAA2B,MAAA;QACA,KAAA8B,QAAA,CAAAM,OAAA;QACA;MACA;MACA,KAAAhE,oBAAA;IACA;IACA,aACAiE,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,SAAAjE,UAAA,CAAA2B,MAAA;QACA,KAAA8B,QAAA,CAAAM,OAAA;QACA;MACA;;MAEA;MACA,SAAAG,CAAA,MAAAA,CAAA,QAAAlE,UAAA,CAAA2B,MAAA,EAAAuC,CAAA;QACA,IAAAnC,KAAA,QAAA/B,UAAA,CAAAkE,CAAA;QACA,KAAAnC,KAAA,CAAAd,KAAA;UACA,KAAAwC,QAAA,CAAAU,KAAA,UAAAC,MAAA,CAAAF,CAAA;UACA;QACA;QACA,qFAAAG,QAAA,CAAAtC,KAAA,CAAAb,IAAA,MAAAa,KAAA,CAAAX,OAAA;UACA,KAAAqC,QAAA,CAAAU,KAAA,kBAAAC,MAAA,CAAArC,KAAA,CAAAd,KAAA;UACA;QACA;MACA;MAEA,IAAAqD,UAAA,GAAA5D,IAAA,CAAA6D,SAAA,MAAAvE,UAAA;MACA,IAAAwE,UAAA;QACAvE,UAAA,OAAAA,UAAA;QACAQ,UAAA,EAAA6D;MACA;MAEA,IAAAG,oCAAA,EAAAD,UAAA,EAAAjE,IAAA,WAAAC,QAAA;QACAyD,MAAA,CAAAS,MAAA,CAAAC,UAAA;QACAV,MAAA,CAAAnE,cAAA;QACAmE,MAAA,CAAA9D,cAAA;MACA;IACA;IACA,eACAyE,gBAAA,WAAAA,iBAAA1D,IAAA;MACA,IAAA2D,SAAA;QACAvC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,OAAA2B,SAAA,CAAA3D,IAAA;IACA;EACA;AACA", "ignoreList": []}]}