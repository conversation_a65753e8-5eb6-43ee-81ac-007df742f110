{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753759379321}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752653991061}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVtYW5kLCBnZXREZW1hbmQsIGRlbERlbWFuZCwgYWRkRGVtYW5kLCB1cGRhdGVEZW1hbmQsIG9mZlNoZWxmRGVtYW5kLCBvblNoZWxmRGVtYW5kLCB1cGRhdGVDb250YWN0U3RhdHVzIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9kZW1hbmQiOw0KaW1wb3J0IHsgZ2V0RW5hYmxlZERlbWFuZENhdGVnb3J5TGlzdCB9IGZyb20gIkAvYXBpL21pbmlhcHAvZGVtYW5kY2F0ZWdvcnkiOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTWluaURlbWFuZCIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6ZyA5rGC6KGo5qC85pWw5o2uDQogICAgICBkZW1hbmRMaXN0OiBbXSwNCiAgICAgIC8vIOiBlOezu+iusOW9leW8ueeqlw0KICAgICAgY29udGFjdERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6IGU57O76K6w5b2V6KGo5Y2VDQogICAgICBjb250YWN0Rm9ybTogew0KICAgICAgICBkb2NraW5nSWQ6IG51bGwsDQogICAgICAgIHVzZXJOYW1lOiAnJywNCiAgICAgICAgdXNlclBob25lOiAnJywNCiAgICAgICAgaXNDb250YWN0ZWQ6ICcwJywNCiAgICAgICAgY29udGFjdFJlc3VsdDogJycsDQogICAgICAgIGNvbnRhY3ROb3RlczogJycsDQogICAgICAgIGNvbnRhY3RUaW1lOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOiBlOezu+iusOW9leihqOWNlemqjOivgQ0KICAgICAgY29udGFjdFJ1bGVzOiB7DQogICAgICAgIGlzQ29udGFjdGVkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaYr+WQpuW3suiBlOezuyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOivpuaDheW8ueeqlw0KICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDor6bmg4XmlbDmja4NCiAgICAgIGRldGFpbEZvcm06IHsNCiAgICAgICAgZG9ja2luZ0xpc3Q6IFtdLA0KICAgICAgICBmb3JtRGF0YUxpc3Q6IFtdDQogICAgICB9LA0KICAgICAgLy8g6KGo5qC85Yi35pawa2V5DQogICAgICB0YWJsZVJlZnJlc2hLZXk6IDAsDQogICAgICAvLyDpnIDmsYLnsbvlnovliJfooagNCiAgICAgIGNhdGVnb3J5TGlzdDogW10sDQogICAgICAvLyDliqjmgIHooajljZXlrZfmrrUNCiAgICAgIGR5bmFtaWNGaWVsZHM6IFtdLA0KICAgICAgLy8g6YCJ5Lit55qE57G75Z6L5ZCN56ewDQogICAgICBzZWxlY3RlZENhdGVnb3J5TmFtZTogJycsDQogICAgICAvLyDliIbnsbvlrZfmrrXmlbDmja7vvIjmlrDmoLzlvI/vvIkNCiAgICAgIGNhdGVnb3J5RmllbGRzRGF0YTogW10sDQogICAgICAvLyDkuIrkvKDor7fmsYLlpLQNCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsNCiAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbg0KICAgICAgfSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBkZW1hbmRUaXRsZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlJZDogbnVsbCwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiBudWxsLA0KICAgICAgICBoYXNEb2NraW5nOiBudWxsLA0KICAgICAgICB0aW1lRmlsdGVyOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY2F0ZWdvcnlJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpnIDmsYLnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGRlbWFuZFRpdGxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguagh+mimOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGRlbWFuZERlc2M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZyA5rGC5o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGU57O75Lq65aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdFBob25lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+S6uueUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL14xWzN8NHw1fDZ8N3w4fDldWzAtOV1cZHs4fSQvLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxgueKtuaAgeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLyoqIOaMieaooeWdl+WIhue7hOeahOWKqOaAgeWtl+autSAqLw0KICAgIGdyb3VwZWREeW5hbWljRmllbGRzKCkgew0KICAgICAgY29uc3QgZ3JvdXBlZCA9IHt9Ow0KICAgICAgdGhpcy5keW5hbWljRmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICBjb25zdCBtb2R1bGVUaXRsZSA9IGZpZWxkLm1vZHVsZVRpdGxlIHx8ICflhbbku5blrZfmrrUnOw0KICAgICAgICBpZiAoIWdyb3VwZWRbbW9kdWxlVGl0bGVdKSB7DQogICAgICAgICAgZ3JvdXBlZFttb2R1bGVUaXRsZV0gPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBncm91cGVkW21vZHVsZVRpdGxlXS5wdXNoKGZpZWxkKTsNCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIGdyb3VwZWQ7DQogICAgfSwNCg0KICAgIC8qKiDlronlhajnmoTliqjmgIHmlbDmja7orr/pl67lmaggKi8NCiAgICBzYWZlRHluYW1pY0RhdGEoKSB7DQogICAgICBjb25zdCBzYWZlRGF0YSA9IHsgLi4udGhpcy5mb3JtLmR5bmFtaWNEYXRhIH07DQogICAgICB0aGlzLmR5bmFtaWNGaWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgIGlmIChmaWVsZC5uYW1lKSB7DQogICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdjaGVja2JveCcgJiYgIUFycmF5LmlzQXJyYXkoc2FmZURhdGFbZmllbGQubmFtZV0pKSB7DQogICAgICAgICAgICBzYWZlRGF0YVtmaWVsZC5uYW1lXSA9IFtdOw0KICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2ZpbGUnICYmICFBcnJheS5pc0FycmF5KHNhZmVEYXRhW2ZpZWxkLm5hbWVdKSkgew0KICAgICAgICAgICAgc2FmZURhdGFbZmllbGQubmFtZV0gPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHNhZmVEYXRhOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldENhdGVnb3J5TGlzdCgpOw0KICAgIC8vIOa1i+ivleaWsOeahOaVsOaNruagvOW8jw0KICAgIHRoaXMudGVzdE5ld0RhdGFGb3JtYXQoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LpnIDmsYLliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3REZW1hbmQodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGVtYW5kTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumcgOaxguWIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W6ZyA5rGC5YiX6KGo5aSx6LSlIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bpnIDmsYLnsbvlnovliJfooaggKi8NCiAgICBnZXRDYXRlZ29yeUxpc3QoKSB7DQogICAgICBnZXRFbmFibGVkRGVtYW5kQ2F0ZWdvcnlMaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6ZyA5rGC57G75Z6L5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPlumcgOaxguexu+Wei+WIl+ihqOWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCg0KDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgZGVtYW5kSWQ6IG51bGwsDQogICAgICAgIGNhdGVnb3J5SWQ6IG51bGwsDQogICAgICAgIGRlbWFuZFRpdGxlOiAiIiwNCiAgICAgICAgZGVtYW5kRGVzYzogIiIsDQogICAgICAgIGNvbnRhY3ROYW1lOiAiIiwNCiAgICAgICAgY29udGFjdFBob25lOiAiIiwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiAiMCIsDQogICAgICAgIGlzVG9wOiAiMCIsDQogICAgICAgIHJlbWFyazogIiIsDQogICAgICAgIGR5bmFtaWNEYXRhOiB7fQ0KICAgICAgfTsNCg0KICAgICAgLy8g5riF6Zmk5Yqo5oCB5a2X5q6155qE6aqM6K+B6KeE5YiZDQogICAgICBPYmplY3Qua2V5cyh0aGlzLnJ1bGVzKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgIGlmIChrZXkuc3RhcnRzV2l0aCgnZHluYW1pY0RhdGEuJykpIHsNCiAgICAgICAgICB0aGlzLiRkZWxldGUodGhpcy5ydWxlcywga2V5KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOmHjee9ruWKqOaAgeWtl+autQ0KICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnlOYW1lID0gJyc7DQoNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmRlbWFuZElkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDpnIDmsYIiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIC8vIOWFiOa4heeQhueKtuaAge+8jOS9huS4jemHjee9ruihqOWNlQ0KICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnlOYW1lID0gJyc7DQoNCiAgICAgIGNvbnN0IGRlbWFuZElkID0gcm93LmRlbWFuZElkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0RGVtYW5kKGRlbWFuZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgLy8g5L2/55SoJHNldOadpeS/neaMgeWTjeW6lOW8jw0KICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2RlbWFuZElkJywgZGF0YS5kZW1hbmRJZCk7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdjYXRlZ29yeUlkJywgZGF0YS5jYXRlZ29yeUlkKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2RlbWFuZFRpdGxlJywgZGF0YS5kZW1hbmRUaXRsZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkZW1hbmREZXNjJywgZGF0YS5kZW1hbmREZXNjIHx8ICIiKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2NvbnRhY3ROYW1lJywgZGF0YS5jb250YWN0TmFtZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdjb250YWN0UGhvbmUnLCBkYXRhLmNvbnRhY3RQaG9uZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkZW1hbmRTdGF0dXMnLCBkYXRhLmRlbWFuZFN0YXR1cyB8fCAiMCIpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnaXNUb3AnLCBkYXRhLmlzVG9wIHx8ICIwIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdyZW1hcmsnLCBkYXRhLnJlbWFyayB8fCAiIik7DQoNCiAgICAgICAgLy8g6Kej5p6Q5Yqo5oCB6KGo5Y2V5pWw5o2uDQogICAgICAgIGlmIChkYXRhLmZvcm1EYXRhKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gSlNPTi5wYXJzZShkYXRhLmZvcm1EYXRhKTsNCg0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv5paw5qC85byP55qE5pWw5o2u77yI5YyF5ZCrZmllbGRz5pWw57uE55qE5a+56LGh77yJDQogICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkgJiYgZm9ybURhdGEubGVuZ3RoID4gMCAmJiBmb3JtRGF0YVswXS5maWVsZHMpIHsNCiAgICAgICAgICAgICAgLy8g5paw5qC85byP77ya5YWI6K6+572u6KGo5Y2V5pWw5o2u77yM5YaN5aSE55CG5YiG57G75a2X5q615pWw5o2uDQogICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkeW5hbWljRGF0YScsIHt9KTsNCg0KICAgICAgICAgICAgICAvLyDku45maWVsZHPkuK3mj5Dlj5bmlbDmja7liLBkeW5hbWljRGF0YQ0KICAgICAgICAgICAgICBmb3JtRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGNhdGVnb3J5RGF0YS5maWVsZHMpIHsNCiAgICAgICAgICAgICAgICAgIGNhdGVnb3J5RGF0YS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZC52YWx1ZSAhPT0gdW5kZWZpbmVkICYmIGZpZWxkLnZhbHVlICE9PSBudWxsICYmIGZpZWxkLnZhbHVlICE9PSAnJykgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkLm5hbWUsIGZpZWxkLnZhbHVlKTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgICAvLyDlpITnkIbliIbnsbvlrZfmrrXmlbDmja4NCiAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzQ2F0ZWdvcnlGaWVsZHNEYXRhKGZvcm1EYXRhKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muebtOaOpeS9v+eUqGZvcm1EYXRh5L2c5Li6ZHluYW1pY0RhdGENCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2R5bmFtaWNEYXRhJywgZm9ybURhdGEpOw0KICAgICAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKHRoaXMuZm9ybS5jYXRlZ29yeUlkKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDliqjmgIHooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnZHluYW1pY0RhdGEnLCB7fSk7DQogICAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKHRoaXMuZm9ybS5jYXRlZ29yeUlkKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2R5bmFtaWNEYXRhJywge30pOw0KICAgICAgICAgIHRoaXMubG9hZER5bmFtaWNGaWVsZHModGhpcy5mb3JtLmNhdGVnb3J5SWQpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5Zyo5LiL5LiA5LiqdGlja+S4rea4hemZpOihqOWNlemqjOivgeeKtuaAgQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuJHJlZnMuZm9ybSkgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KDQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56ZyA5rGCIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAvLyDlhYjpqozor4HliqjmgIHlrZfmrrUNCiAgICAgIGxldCBkeW5hbWljRmllbGRzVmFsaWQgPSB0cnVlOw0KICAgICAgbGV0IGZpcnN0RXJyb3JGaWVsZCA9IG51bGw7DQoNCiAgICAgIC8vIOmqjOivgeaWsOagvOW8j+eahOWIhuexu+Wtl+auteaVsOaNrg0KICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEuZm9yRWFjaChjYXRlZ29yeURhdGEgPT4gew0KICAgICAgICAgIGlmIChjYXRlZ29yeURhdGEuZmllbGRzKSB7DQogICAgICAgICAgICBjYXRlZ29yeURhdGEuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgICAgICBpZiAoZmllbGQucmVxdWlyZWQgJiYgZmllbGQubmFtZSAmJiBmaWVsZC50eXBlICE9PSAnc3RhdGljJykgew0KICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdOw0KICAgICAgICAgICAgICAgIGxldCBpc0VtcHR5ID0gZmFsc2U7DQoNCiAgICAgICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94JyB8fCBmaWVsZC50eXBlID09PSAnZmlsZScpIHsNCiAgICAgICAgICAgICAgICAgIGlzRW1wdHkgPSAhQXJyYXkuaXNBcnJheSh2YWx1ZSkgfHwgdmFsdWUubGVuZ3RoID09PSAwOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBpc0VtcHR5ID0gdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJyc7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgaWYgKGlzRW1wdHkpIHsNCiAgICAgICAgICAgICAgICAgIGR5bmFtaWNGaWVsZHNWYWxpZCA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgaWYgKCFmaXJzdEVycm9yRmllbGQpIHsNCiAgICAgICAgICAgICAgICAgICAgZmlyc3RFcnJvckZpZWxkID0gZmllbGQubGFiZWw7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g6aqM6K+B5pen5qC85byP55qE5Yqo5oCB5a2X5q61DQogICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICBpZiAoZmllbGQucmVxdWlyZWQgJiYgZmllbGQubmFtZSkgew0KICAgICAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV07DQogICAgICAgICAgICBsZXQgaXNFbXB0eSA9IGZhbHNlOw0KDQogICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94JyB8fCBmaWVsZC50eXBlID09PSAnZmlsZScpIHsNCiAgICAgICAgICAgICAgaXNFbXB0eSA9ICFBcnJheS5pc0FycmF5KHZhbHVlKSB8fCB2YWx1ZS5sZW5ndGggPT09IDA7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBpc0VtcHR5ID0gdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJyc7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGlmIChpc0VtcHR5KSB7DQogICAgICAgICAgICAgIGR5bmFtaWNGaWVsZHNWYWxpZCA9IGZhbHNlOw0KICAgICAgICAgICAgICBpZiAoIWZpcnN0RXJyb3JGaWVsZCkgew0KICAgICAgICAgICAgICAgIGZpcnN0RXJyb3JGaWVsZCA9IGZpZWxkLmxhYmVsOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgaWYgKCFkeW5hbWljRmllbGRzVmFsaWQpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYCR7Zmlyc3RFcnJvckZpZWxkfeS4jeiDveS4uuepumApOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0geyAuLi50aGlzLmZvcm0gfTsNCg0KICAgICAgICAgIC8vIOaehOW7uuWMheWQq3ZhbHVl55qE5a6M5pW05a2X5q615pWw5o2u5qC85byPDQogICAgICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIC8vIOaWsOagvOW8j++8muS9v+eUqOWIhuexu+Wtl+auteaVsOaNru+8jOW5tuabtOaWsOavj+S4quWtl+auteeahHZhbHVlDQogICAgICAgICAgICBjb25zdCBjYXRlZ29yeURhdGFXaXRoVmFsdWVzID0gdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEubWFwKGNhdGVnb3J5RGF0YSA9PiAoew0KICAgICAgICAgICAgICAuLi5jYXRlZ29yeURhdGEsDQogICAgICAgICAgICAgIGZpZWxkczogY2F0ZWdvcnlEYXRhLmZpZWxkcy5tYXAoZmllbGQgPT4gKHsNCiAgICAgICAgICAgICAgICAuLi5maWVsZCwNCiAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdIHx8IGZpZWxkLnZhbHVlIHx8IChmaWVsZC50eXBlID09PSAnY2hlY2tib3gnIHx8IGZpZWxkLnR5cGUgPT09ICdmaWxlJyA/IFtdIDogJycpDQogICAgICAgICAgICAgIH0pKQ0KICAgICAgICAgICAgfSkpOw0KICAgICAgICAgICAgZm9ybURhdGEuZm9ybURhdGEgPSBKU09OLnN0cmluZ2lmeShjYXRlZ29yeURhdGFXaXRoVmFsdWVzKTsNCiAgICAgICAgICB9IGVsc2UgaWYgKGZvcm1EYXRhLmR5bmFtaWNEYXRhICYmIE9iamVjdC5rZXlzKGZvcm1EYXRhLmR5bmFtaWNEYXRhKS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDml6fmoLzlvI/vvJrnm7TmjqXkvb/nlKhkeW5hbWljRGF0YQ0KICAgICAgICAgICAgZm9ybURhdGEuZm9ybURhdGEgPSBKU09OLnN0cmluZ2lmeShmb3JtRGF0YS5keW5hbWljRGF0YSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgZGVsZXRlIGZvcm1EYXRhLmR5bmFtaWNEYXRhOyAvLyDliKDpmaTkuLTml7blrZfmrrUNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCdzdWJtaXRGb3JtIC0gZm9ybURhdGEuZm9ybURhdGE6JywgZm9ybURhdGEuZm9ybURhdGEpOw0KDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5kZW1hbmRJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZW1hbmQoZm9ybURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERlbWFuZChmb3JtRGF0YSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5p+l55yL6K+m5oOFICovDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgY29uc29sZS5sb2coJ+afpeeci+ivpuaDhSAtIOWOn+Wni+aVsOaNrjonLCByb3cpOw0KDQogICAgICAvLyDkvb/nlKhWdWUuc2V056Gu5L+d5ZON5bqU5byPDQogICAgICB0aGlzLiRzZXQodGhpcywgJ2RldGFpbEZvcm0nLCB7DQogICAgICAgIC4uLnJvdywNCiAgICAgICAgZG9ja2luZ0xvYWRpbmc6IHRydWUsDQogICAgICAgIGRvY2tpbmdMaXN0OiBbXSwNCiAgICAgICAgZm9ybURhdGFMaXN0OiBbXQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOino+aekOihqOWNleaVsOaNrg0KICAgICAgaWYgKHJvdy5mb3JtRGF0YSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gSlNPTi5wYXJzZShyb3cuZm9ybURhdGEpOw0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdmb3JtRGF0YUxpc3QnLCB0aGlzLnBhcnNlRm9ybURhdGFGb3JEaXNwbGF5KGZvcm1EYXRhKSk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2Zvcm1EYXRhTGlzdCcsIFtdKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn6K+m5oOF6KGo5Y2V5pWw5o2uOicsIHRoaXMuZGV0YWlsRm9ybSk7DQoNCiAgICAgIC8vIOaJk+W8gOW8ueeqlw0KICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCg0KICAgICAgLy8g5Yqg6L295a+55o6l6K6w5b2VDQogICAgICByZXF1ZXN0KHsNCiAgICAgICAgdXJsOiBgL21pbmlhcHAvZGVtYW5kLyR7cm93LmRlbWFuZElkfS9kb2NraW5nc2AsDQogICAgICAgIG1ldGhvZDogJ2dldCcNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygn5a+55o6l6K6w5b2V5ZON5bqUOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTGlzdCcsIHJlc3BvbnNlLmRhdGEgfHwgW10pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTGlzdCcsIFtdKTsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmjqXorrDlvZXlpLHotKU6JywgcmVzcG9uc2UubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmjqXorrDlvZXlvILluLg6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCBbXSk7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2RvY2tpbmdMb2FkaW5nJywgZmFsc2UpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDogZTns7vorrDlvZXmk43kvZwgKi8NCiAgICBoYW5kbGVDb250YWN0UmVjb3JkKGRvY2tpbmdSb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmiZPlvIDogZTns7vorrDlvZXlvLnnqpfvvIzlr7nmjqXorrDlvZXmlbDmja46JywgZG9ja2luZ1Jvdyk7DQogICAgICB0aGlzLmNvbnRhY3RGb3JtID0gew0KICAgICAgICBkb2NraW5nSWQ6IGRvY2tpbmdSb3cuZG9ja2luZ0lkLA0KICAgICAgICB1c2VyTmFtZTogZG9ja2luZ1Jvdy51c2VyTmFtZSwNCiAgICAgICAgdXNlclBob25lOiBkb2NraW5nUm93LnVzZXJQaG9uZSwNCiAgICAgICAgaXNDb250YWN0ZWQ6IGRvY2tpbmdSb3cuaXNDb250YWN0ZWQgfHwgJzAnLA0KICAgICAgICBjb250YWN0UmVzdWx0OiBkb2NraW5nUm93LmNvbnRhY3RSZXN1bHQgfHwgJycsDQogICAgICAgIGNvbnRhY3ROb3RlczogZG9ja2luZ1Jvdy5jb250YWN0Tm90ZXMgfHwgJycsDQogICAgICAgIGNvbnRhY3RUaW1lOiBkb2NraW5nUm93LmNvbnRhY3RUaW1lIHx8ICcnDQogICAgICB9Ow0KICAgICAgY29uc29sZS5sb2coJ+iBlOezu+ihqOWNleaVsOaNrjonLCB0aGlzLmNvbnRhY3RGb3JtKTsNCiAgICAgIHRoaXMuY29udGFjdERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk6IGU57O76K6w5b2V6KGo5Y2VICovDQogICAgc3VibWl0Q29udGFjdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJjb250YWN0Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g5aaC5p6c6YCJ5oup5bey6IGU57O75L2G5rKh5pyJ6K6+572u6IGU57O75pe26Ze077yM5L2/55So5b2T5YmN5pe26Ze0DQogICAgICAgICAgaWYgKHRoaXMuY29udGFjdEZvcm0uaXNDb250YWN0ZWQgPT09ICcxJyAmJiAhdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZSkgew0KICAgICAgICAgICAgdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZSA9IHRoaXMucGFyc2VUaW1lKG5ldyBEYXRlKCksICd7eX0te219LXtkfSB7aH06e2l9OntzfScpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS/neWtmOW9k+WJjeiBlOezu+eKtuaAge+8jOeUqOS6juebtOaOpeabtOaWsOacrOWcsOaVsOaNrg0KICAgICAgICAgIGNvbnN0IGRvY2tpbmdJZCA9IHRoaXMuY29udGFjdEZvcm0uZG9ja2luZ0lkOw0KICAgICAgICAgIGNvbnN0IG5ld0lzQ29udGFjdGVkID0gdGhpcy5jb250YWN0Rm9ybS5pc0NvbnRhY3RlZDsNCiAgICAgICAgICBjb25zdCBuZXdDb250YWN0UmVzdWx0ID0gdGhpcy5jb250YWN0Rm9ybS5jb250YWN0UmVzdWx0Ow0KICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3ROb3RlcyA9IHRoaXMuY29udGFjdEZvcm0uY29udGFjdE5vdGVzOw0KICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RUaW1lID0gdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZTsNCg0KICAgICAgICAgIHVwZGF0ZUNvbnRhY3RTdGF0dXModGhpcy5jb250YWN0Rm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfogZTns7vnirbmgIHmm7TmlrDmiJDlip86JywgcmVzcG9uc2UpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6IGU57O76K6w5b2V5pu05paw5oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLmNvbnRhY3REaWFsb2dWaXNpYmxlID0gZmFsc2U7DQoNCiAgICAgICAgICAgIC8vIOWmguaenOivpuaDheW8ueeql+aYr+aJk+W8gOeahO+8jOWFiOebtOaOpeabtOaWsOacrOWcsOaVsOaNru+8jOWGjeWIt+aWsOivpuaDheS4reeahOWvueaOpeiusOW9lQ0KICAgICAgICAgICAgaWYgKHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSAmJiB0aGlzLmRldGFpbEZvcm0uZGVtYW5kSWQgJiYgdGhpcy5kZXRhaWxGb3JtLmRvY2tpbmdMaXN0KSB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vmm7TmlrDmnKzlnLDlr7nmjqXorrDlvZUuLi4nKTsNCg0KICAgICAgICAgICAgICAvLyDlhYjnm7TmjqXmm7TmlrDmnKzlnLDmlbDmja7vvIznq4vljbPlj43mmKDlj5jljJYNCiAgICAgICAgICAgICAgY29uc3QgZG9ja2luZ0l0ZW0gPSB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QuZmluZChpdGVtID0+IGl0ZW0uZG9ja2luZ0lkID09PSBkb2NraW5nSWQpOw0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5om+5Yiw55qE5a+55o6l6K6w5b2VOicsIGRvY2tpbmdJdGVtKTsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+imgeabtOaWsOeahOiBlOezu+eKtuaAgTonLCBuZXdJc0NvbnRhY3RlZCk7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvZPliY1kb2NraW5nTGlzdDonLCB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QpOw0KDQogICAgICAgICAgICAgIGlmIChkb2NraW5nSXRlbSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDliY3nmoTogZTns7vnirbmgIE6JywgZG9ja2luZ0l0ZW0uaXNDb250YWN0ZWQpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2lzQ29udGFjdGVkJywgbmV3SXNDb250YWN0ZWQpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2NvbnRhY3RSZXN1bHQnLCBuZXdDb250YWN0UmVzdWx0KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXQoZG9ja2luZ0l0ZW0sICdjb250YWN0Tm90ZXMnLCBuZXdDb250YWN0Tm90ZXMpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2NvbnRhY3RUaW1lJywgbmV3Q29udGFjdFRpbWUpOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDlkI7nmoTogZTns7vnirbmgIE6JywgZG9ja2luZ0l0ZW0uaXNDb250YWN0ZWQpOw0KDQogICAgICAgICAgICAgICAgLy8g5by65Yi25Yi35paw6KGo5qC8DQogICAgICAgICAgICAgICAgdGhpcy50YWJsZVJlZnJlc2hLZXkrKzsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5by65Yi25Yi35paw6KGo5qC877yM5pawa2V5OicsIHRoaXMudGFibGVSZWZyZXNoS2V5KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmnKrmib7liLDlr7nlupTnmoTlr7nmjqXorrDlvZXvvIxkb2NraW5nSWQ6JywgZG9ja2luZ0lkKTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmiYDmnInlr7nmjqXorrDlvZXnmoRJRDonLCB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QubWFwKGl0ZW0gPT4gaXRlbS5kb2NraW5nSWQpKTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOeEtuWQjuWGjeS7juacjeWKoeWZqOWIt+aWsOWujOaVtOaVsOaNrg0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5byA5aeL5Yi35paw6K+m5oOF5Lit55qE5a+55o6l6K6w5b2VLi4uJyk7DQogICAgICAgICAgICAgIHRoaXMucmVmcmVzaERldGFpbERvY2tpbmdMaXN0KCk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWIt+aWsOS4u+WIl+ihqOS7peabtOaWsOe7n+iuoeaVsOaNrg0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfogZTns7vnirbmgIHmm7TmlrDlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuabtOaWsOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWIt+aWsOivpuaDheS4reeahOWvueaOpeiusOW9lSAqLw0KICAgIHJlZnJlc2hEZXRhaWxEb2NraW5nTGlzdCgpIHsNCiAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTG9hZGluZycsIHRydWUpOw0KICAgICAgcmVxdWVzdCh7DQogICAgICAgIHVybDogYC9taW5pYXBwL2RlbWFuZC8ke3RoaXMuZGV0YWlsRm9ybS5kZW1hbmRJZH0vZG9ja2luZ3NgLA0KICAgICAgICBtZXRob2Q6ICdnZXQnDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ+WIt+aWsOWvueaOpeiusOW9leWTjeW6lDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCByZXNwb25zZS5kYXRhIHx8IFtdKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pu05paw5ZCO55qE5a+55o6l6K6w5b2VOicsIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdCk7DQogICAgICAgICAgLy8g5by65Yi25Yi35paw6KGo5qC8DQogICAgICAgICAgdGhpcy50YWJsZVJlZnJlc2hLZXkrKzsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pyN5Yqh5Zmo5pWw5o2u5Yi35paw5ZCO77yM5by65Yi25Yi35paw6KGo5qC877yM5pawa2V5OicsIHRoaXMudGFibGVSZWZyZXNoS2V5KTsNCiAgICAgICAgICAvLyDosIPor5XmlbDmja4NCiAgICAgICAgICB0aGlzLmRlYnVnRG9ja2luZ0RhdGEoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCBbXSk7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55o6l6K6w5b2V5aSx6LSlOicsIHJlc3BvbnNlLm1zZyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55o6l6K6w5b2V5byC5bi4OicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2RvY2tpbmdMaXN0JywgW10pOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTG9hZGluZycsIGZhbHNlKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6Kej5p6Q6KGo5Y2V5pWw5o2u5Li65pi+56S65qC85byPICovDQogICAgcGFyc2VGb3JtRGF0YUZvckRpc3BsYXkoZm9ybURhdGEpIHsNCiAgICAgIGNvbnN0IGRpc3BsYXlMaXN0ID0gW107DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+aWsOagvOW8j+eahOaVsOaNru+8iOWMheWQq2ZpZWxkc+aVsOe7hOeahOWvueixoe+8iQ0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkgJiYgZm9ybURhdGEubGVuZ3RoID4gMCAmJiBmb3JtRGF0YVswXS5maWVsZHMpIHsNCiAgICAgICAgICAvLyDmlrDmoLzlvI/vvJrpgY3ljobmiYDmnInliIbnsbvlkozlrZfmrrUNCiAgICAgICAgICBmb3JtRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgICAgICBpZiAoY2F0ZWdvcnlEYXRhLmZpZWxkcyAmJiBBcnJheS5pc0FycmF5KGNhdGVnb3J5RGF0YS5maWVsZHMpKSB7DQogICAgICAgICAgICAgIGNhdGVnb3J5RGF0YS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgLy8g6Lez6L+H6Z2Z5oCB5bGV56S65a2X5q61DQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgIT09ICdzdGF0aWMnICYmIGZpZWxkLm5hbWUgJiYgZmllbGQudmFsdWUgIT09IHVuZGVmaW5lZCAmJiBmaWVsZC52YWx1ZSAhPT0gbnVsbCAmJiBmaWVsZC52YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICAgIGRpc3BsYXlMaXN0LnB1c2goew0KICAgICAgICAgICAgICAgICAgICBsYWJlbDogZmllbGQubGFiZWwgfHwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGZpZWxkLnZhbHVlLA0KICAgICAgICAgICAgICAgICAgICB0eXBlOiBmaWVsZC50eXBlIHx8ICdpbnB1dCcNCiAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGZvcm1EYXRhID09PSAnb2JqZWN0JyAmJiBmb3JtRGF0YSAhPT0gbnVsbCkgew0KICAgICAgICAgIC8vIOaXp+agvOW8j++8muebtOaOpemBjeWOhuWvueixoeWxnuaApw0KICAgICAgICAgIE9iamVjdC5rZXlzKGZvcm1EYXRhKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGZvcm1EYXRhW2tleV07DQogICAgICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgZGlzcGxheUxpc3QucHVzaCh7DQogICAgICAgICAgICAgICAgbGFiZWw6IGtleSwNCiAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUsDQogICAgICAgICAgICAgICAgdHlwZTogJ2lucHV0JyAvLyDpu5jorqTnsbvlnosNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGo5Y2V5pWw5o2u5aSx6LSlOicsIGUpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gZGlzcGxheUxpc3Q7DQogICAgfSwNCg0KICAgIC8qKiDku45VUkzkuK3mj5Dlj5bmlofku7blkI0gKi8NCiAgICBnZXRGaWxlTmFtZUZyb21VcmwodXJsKSB7DQogICAgICBpZiAoIXVybCkgcmV0dXJuICfmnKrnn6Xmlofku7YnOw0KICAgICAgY29uc3QgcGFydHMgPSB1cmwuc3BsaXQoJy8nKTsNCiAgICAgIHJldHVybiBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXSB8fCAn5pyq55+l5paH5Lu2JzsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluiBlOezu+e7k+aenOagh+etvuexu+WeiyAqLw0KICAgIGdldENvbnRhY3RSZXN1bHRUeXBlKHJlc3VsdCkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgJ+iBlOezu+aIkOWKnyc6ICdzdWNjZXNzJywNCiAgICAgICAgJ+W3suacieWQiOS9nCc6ICdzdWNjZXNzJywNCiAgICAgICAgJ+aXoOS6uuaOpeWQrCc6ICd3YXJuaW5nJywNCiAgICAgICAgJ+eojeWQjuiBlOezuyc6ICd3YXJuaW5nJywNCiAgICAgICAgJ+WPt+eggemUmeivryc6ICdkYW5nZXInLA0KICAgICAgICAn5ouS57ud5rKf6YCaJzogJ2RhbmdlcicsDQogICAgICAgICfkuI3mhJ/lhbTotqMnOiAnaW5mbycsDQogICAgICAgICflhbbku5YnOiAnaW5mbycNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtyZXN1bHRdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLyoqIOiwg+ivle+8muajgOafpeW9k+WJjeWvueaOpeiusOW9leaVsOaNriAqLw0KICAgIGRlYnVnRG9ja2luZ0RhdGEoKSB7DQogICAgICBjb25zb2xlLmxvZygnPT09IOiwg+ivleWvueaOpeiusOW9leaVsOaNriA9PT0nKTsNCiAgICAgIGNvbnNvbGUubG9nKCdkZXRhaWxGb3JtLmRvY2tpbmdMaXN0OicsIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdCk7DQogICAgICBpZiAodGhpcy5kZXRhaWxGb3JtLmRvY2tpbmdMaXN0ICYmIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGDorrDlvZUke2luZGV4ICsgMX06YCwgew0KICAgICAgICAgICAgZG9ja2luZ0lkOiBpdGVtLmRvY2tpbmdJZCwNCiAgICAgICAgICAgIHVzZXJOYW1lOiBpdGVtLnVzZXJOYW1lLA0KICAgICAgICAgICAgaXNDb250YWN0ZWQ6IGl0ZW0uaXNDb250YWN0ZWQsDQogICAgICAgICAgICBpc0NvbnRhY3RlZFR5cGU6IHR5cGVvZiBpdGVtLmlzQ29udGFjdGVkDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coJ3RhYmxlUmVmcmVzaEtleTonLCB0aGlzLnRhYmxlUmVmcmVzaEtleSk7DQogICAgICBjb25zb2xlLmxvZygnPT09PT09PT09PT09PT09PT09PT09PT09Jyk7DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBkZW1hbmRJZHMgPSByb3cgPyBbcm93LmRlbWFuZElkXSA6IHRoaXMuaWRzOw0KICAgICAgY29uc3QgY29uZmlybVRleHQgPSByb3cNCiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6Zmk6ZyA5rGC57yW5Y+35Li6IiR7cm93LmRlbWFuZElkfSLnmoTmlbDmja7pobnvvJ9gDQogICAgICAgIDogYOaYr+WQpuehruiupOWIoOmZpOmAieS4reeahCR7dGhpcy5pZHMubGVuZ3RofeadoeaVsOaNrumhue+8n2A7DQoNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oY29uZmlybVRleHQpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxEZW1hbmQoZGVtYW5kSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC9kZW1hbmQvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBg6ZyA5rGC5pWw5o2uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLyoqIOe9rumhti/lj5bmtojnva7pobYgKi8NCiAgICBoYW5kbGVUb2dnbGVUb3Aocm93KSB7DQogICAgICBjb25zdCB0ZXh0ID0gcm93LmlzVG9wID09PSAiMSIgPyAi5Y+W5raI572u6aG2IiA6ICLnva7pobYiOw0KICAgICAgY29uc3QgaXNUb3AgPSByb3cuaXNUb3AgPT09ICIxIiA/ICIwIiA6ICIxIjsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICci6ZyA5rGCIicgKyByb3cuZGVtYW5kVGl0bGUgKyAnIuWQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgZGVtYW5kSWQ6IHJvdy5kZW1hbmRJZCwNCiAgICAgICAgICBpc1RvcDogaXNUb3ANCiAgICAgICAgfTsNCiAgICAgICAgcmV0dXJuIHVwZGF0ZURlbWFuZCh1cGRhdGVEYXRhKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5LiL5p626ZyA5rGCICovDQogICAgaGFuZGxlT2ZmU2hlbGYocm93KSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoHkuIvmnrbpnIDmsYIiJyArIHJvdy5kZW1hbmRUaXRsZSArICci5ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIG9mZlNoZWxmRGVtYW5kKHJvdy5kZW1hbmRJZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS4i+aetuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5LiK5p626ZyA5rGCICovDQogICAgaGFuZGxlT25TaGVsZihyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgeS4iuaetumcgOaxgiInICsgcm93LmRlbWFuZFRpdGxlICsgJyLlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gb25TaGVsZkRlbWFuZChyb3cuZGVtYW5kSWQpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkuIrmnrbmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOmcgOaxguexu+Wei+WPmOWMluS6i+S7tiAqLw0KICAgIG9uQ2F0ZWdvcnlDaGFuZ2UoY2F0ZWdvcnlJZCkgew0KICAgICAgY29uc29sZS5sb2coJ29uQ2F0ZWdvcnlDaGFuZ2UgLSBjYXRlZ29yeUlkOicsIGNhdGVnb3J5SWQpOw0KDQogICAgICAvLyDmuIXnqbrliqjmgIHooajljZXmlbDmja4NCiAgICAgIHRoaXMuZm9ybS5keW5hbWljRGF0YSA9IHt9Ow0KICAgICAgLy8g5riF56m65YiG57G75a2X5q615pWw5o2uDQogICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSA9IFtdOw0KDQogICAgICBpZiAoIWNhdGVnb3J5SWQpIHsNCiAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSAnJzsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcnlMaXN0LmZpbmQoY2F0ID0+IGNhdC5jYXRlZ29yeUlkID09PSBjYXRlZ29yeUlkKTsNCiAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gZm91bmQgY2F0ZWdvcnk6JywgY2F0ZWdvcnkpOw0KDQogICAgICBpZiAoY2F0ZWdvcnkgJiYgY2F0ZWdvcnkuZm9ybUZpZWxkcykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IGZvcm1Db25maWcgPSBKU09OLnBhcnNlKGNhdGVnb3J5LmZvcm1GaWVsZHMpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gZm9ybUNvbmZpZzonLCBmb3JtQ29uZmlnKTsNCg0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+aWsOagvOW8j+eahOaVsOaNru+8iOWMheWQq2ZpZWxkc+aVsOe7hOeahOWvueixoe+8iQ0KICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGZvcm1Db25maWcpICYmIGZvcm1Db25maWcubGVuZ3RoID4gMCAmJiBmb3JtQ29uZmlnWzBdLmZpZWxkcykgew0KICAgICAgICAgICAgLy8g5paw5qC85byP77ya5L2/55So5YiG57G75a2X5q615pWw5o2uDQogICAgICAgICAgICB0aGlzLnByb2Nlc3NDYXRlZ29yeUZpZWxkc0RhdGEoZm9ybUNvbmZpZyk7DQogICAgICAgICAgICBjb25zb2xlLmxvZygnb25DYXRlZ29yeUNoYW5nZSAtIHVzaW5nIG5ldyBmb3JtYXQsIGNhdGVnb3J5RmllbGRzRGF0YTonLCB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muS9v+eUqOS8oOe7n+eahOWKqOaAgeWtl+auteWKoOi9vQ0KICAgICAgICAgICAgdGhpcy5sb2FkRHluYW1pY0ZpZWxkcyhjYXRlZ29yeUlkKTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gdXNpbmcgb2xkIGZvcm1hdCwgZHluYW1pY0ZpZWxkczonLCB0aGlzLmR5bmFtaWNGaWVsZHMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNlemFjee9ruWksei0pTonLCBlKTsNCiAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKGNhdGVnb3J5SWQpOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmxvZygnb25DYXRlZ29yeUNoYW5nZSAtIG5vIGNhdGVnb3J5IG9yIGZvcm1GaWVsZHMgZm91bmQnKTsNCiAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSAnJzsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWKoOi9veWKqOaAgeihqOWNleWtl+autSAqLw0KICAgIGxvYWREeW5hbWljRmllbGRzKGNhdGVnb3J5SWQpIHsNCiAgICAgIGlmICghY2F0ZWdvcnlJZCkgew0KICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCiAgICAgICAgdGhpcy5zZWxlY3RlZENhdGVnb3J5TmFtZSA9ICcnOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yeUxpc3QuZmluZChjYXQgPT4gY2F0LmNhdGVnb3J5SWQgPT09IGNhdGVnb3J5SWQpOw0KICAgICAgaWYgKGNhdGVnb3J5KSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSBjYXRlZ29yeS5jYXRlZ29yeU5hbWU7DQoNCiAgICAgICAgaWYgKGNhdGVnb3J5LmZvcm1GaWVsZHMpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgZm9ybUNvbmZpZyA9IEpTT04ucGFyc2UoY2F0ZWdvcnkuZm9ybUZpZWxkcyk7DQogICAgICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCg0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv5paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtQ29uZmlnKSAmJiBmb3JtQ29uZmlnLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgaWYgKGZvcm1Db25maWdbMF0uZmllbGRzKSB7DQogICAgICAgICAgICAgICAgLy8g5paw55qE5qih5Z2X5YyW57uT5p6E77ya5o+Q5Y+W5omA5pyJ5qih5Z2X5Lit55qE5a2X5q61DQogICAgICAgICAgICAgICAgZm9ybUNvbmZpZy5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAobW9kdWxlLmZpZWxkcyAmJiBBcnJheS5pc0FycmF5KG1vZHVsZS5maWVsZHMpKSB7DQogICAgICAgICAgICAgICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H6Z2Z5oCB5bGV56S65a2X5q61DQogICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgIT09ICdzdGF0aWMnICYmIGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5wdXNoKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmllbGQsDQogICAgICAgICAgICAgICAgICAgICAgICAgIG1vZHVsZVRpdGxlOiBtb2R1bGUubmFtZSAvLyDmt7vliqDmqKHlnZfmoIfpopjnlKjkuo7liIbnu4TmmL7npLoNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5pen55qE5omB5bmz57uT5p6E77ya55u05o6l5L2/55SoDQogICAgICAgICAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gZm9ybUNvbmZpZzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDliJ3lp4vljJbliqjmgIHmlbDmja7lr7nosaHlkozpqozor4Hop4TliJkNCiAgICAgICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgICAvLyDnoa7kv53lrZfmrrXmgLvmmK/mnInmraPnoa7nmoTliJ3lp4vlgLwNCiAgICAgICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94Jykgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheSh0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0pID8gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdIDogW10pOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2ZpbGUnKSB7DQogICAgICAgICAgICAgICAgICAvLyDlpITnkIbmlofku7blrZfmrrXnmoTmlbDmja7ovazmjaINCiAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGVEYXRhID0gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdOw0KICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBmaWxlRGF0YSA9PT0gJ3N0cmluZycgJiYgZmlsZURhdGEudHJpbSgpICE9PSAnJykgew0KICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/lrZfnrKbkuLJVUkzvvIzovazmjaLkuLrlr7nosaHmlbDnu4TmoLzlvI8NCiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBmaWxlRGF0YS5zcGxpdCgnLycpLnBvcCgpIHx8ICfkuIvovb3mlofku7YnOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmR5bmFtaWNEYXRhLCBmaWVsZC5uYW1lLCBbew0KICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICAgICAgICAgIHVybDogZmlsZURhdGENCiAgICAgICAgICAgICAgICAgICAgfV0pOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KGZpbGVEYXRhKSkgew0KICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzlt7Lnu4/mmK/mlbDnu4TvvIzkv53mjIHkuI3lj5gNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmlsZURhdGEpOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgLy8g5YW25LuW5oOF5Ya16K6+5Li656m65pWw57uEDQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkLm5hbWUsIFtdKTsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLnR5cGUgPT09ICdudW1iZXInKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmR5bmFtaWNEYXRhLCBmaWVsZC5uYW1lLA0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gIT09IHVuZGVmaW5lZCA/IHRoaXMuZm9ybS5keW5hbWljRGF0YVtmaWVsZC5uYW1lXSA6IG51bGwpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2RhdGUnIHx8IGZpZWxkLnR5cGUgPT09ICd0aW1lJykgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gOiBudWxsKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gOiAnJyk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgLy8g5re75Yqg5Yqo5oCB5a2X5q6155qE6aqM6K+B6KeE5YiZDQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLnJlcXVpcmVkKSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBydWxlTmFtZSA9IGBkeW5hbWljRGF0YS4ke2ZpZWxkLm5hbWV9YDsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVzLCBydWxlTmFtZSwgWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7ZmllbGQubGFiZWx95LiN6IO95Li656m6YCwNCiAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiBmaWVsZC50eXBlID09PSAnY2hlY2tib3gnID8gJ2NoYW5nZScgOiAnYmx1cicNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgXSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXlrZfmrrXphY3nva7lpLHotKU6JywgZSk7DQogICAgICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluWtl+autemAiemhuSAqLw0KICAgIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkgew0KICAgICAgaWYgKCFmaWVsZC5vcHRpb25zKSByZXR1cm4gW107DQogICAgICByZXR1cm4gZmllbGQub3B0aW9ucy5zcGxpdCgnLCcpLm1hcChvcHRpb24gPT4gb3B0aW9uLnRyaW0oKSkuZmlsdGVyKG9wdGlvbiA9PiBvcHRpb24pOw0KICAgIH0sDQoNCiAgICAvKiog5paH5Lu25LiK5Lyg5oiQ5Yqf5Zue6LCDICovDQogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0LCBmaWVsZCkgew0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUZpbGVTdWNjZXNzIC0gcmVzcG9uc2U6JywgcmVzcG9uc2UsICdmaWxlOicsIGZpbGUsICdmaWVsZDonLCBmaWVsZC5uYW1lKTsNCg0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICBjb25zdCBmaWxlVXJsID0gcmVzcG9uc2UudXJsIHx8IHJlc3BvbnNlLmZpbGVOYW1lIHx8IHJlc3BvbnNlLmRhdGE7DQoNCiAgICAgICAgLy8g5a+55LqO5paH5Lu257G75Z6L5a2X5q6177yMdmFsdWXnm7TmjqXlrZjlgqhVUkzpk77mjqXvvIzkuI3lrZjlgqjmlofku7blkI3miJblr7nosaHnu5PmnoQNCiAgICAgICAgdGhpcy5oYW5kbGVGaWVsZElucHV0KGZpZWxkLCBmaWxlVXJsKTsNCg0KICAgICAgICBjb25zb2xlLmxvZygnaGFuZGxlRmlsZVN1Y2Nlc3MgLSDmlofku7bkuIrkvKDmiJDlip/vvIzorr7nva5VUkw6JywgZmlsZVVybCk7DQogICAgICAgIGNvbnNvbGUubG9nKCdoYW5kbGVGaWxlU3VjY2VzcyAtIGZpZWxkLnZhbHVlIGFmdGVyIHVwZGF0ZTonLCBmaWVsZC52YWx1ZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5paH5Lu25Yig6Zmk5Zue6LCDICovDQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCwgZmllbGQpIHsNCiAgICAgIC8vIOaWh+S7tuWIoOmZpOaXtu+8jOebtOaOpea4heepunZhbHVl5a2X5q61DQogICAgICB0aGlzLmhhbmRsZUZpZWxkSW5wdXQoZmllbGQsICcnKTsNCiAgICAgIGNvbnNvbGUubG9nKCdoYW5kbGVGaWxlUmVtb3ZlIC0g5paH5Lu25bey5Yig6Zmk77yM5riF56m65a2X5q615YC8Jyk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blpJrpgInmoYbnmoTlronlhajlgLwgKi8NCiAgICBnZXRDaGVja2JveFZhbHVlKGZpZWxkTmFtZSkgew0KICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGROYW1lXTsNCiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlIDogW107DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDlpJrpgInmoYbnmoTlgLwgKi8NCiAgICB1cGRhdGVDaGVja2JveFZhbHVlKGZpZWxkTmFtZSwgdmFsdWUpIHsNCiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkTmFtZSwgQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZSA6IFtdKTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluaWh+S7tuWIl+ihqO+8iOeUqOS6jmVsLXVwbG9hZOe7hOS7tu+8iSAqLw0KICAgIGdldEZpbGVMaXN0KGZpZWxkKSB7DQogICAgICBjb25zdCBmaWxlcyA9IGZpZWxkLnZhbHVlOw0KICAgICAgY29uc29sZS5sb2coJ2dldEZpbGVMaXN0IC0gZmllbGQ6JywgZmllbGQubmFtZSwgJ3ZhbHVlOicsIGZpbGVzKTsNCg0KICAgICAgLy8g5aaC5p6c5piv5a2X56ym5LiyVVJM5LiU5LiN5Li656m677yM6L2s5o2i5Li65paH5Lu25YiX6KGo5qC85byP5pi+56S65ZyodXBsb2Fk57uE5Lu25LitDQogICAgICBpZiAodHlwZW9mIGZpbGVzID09PSAnc3RyaW5nJyAmJiBmaWxlcy50cmltKCkgIT09ICcnKSB7DQogICAgICAgIHJldHVybiBbew0KICAgICAgICAgIG5hbWU6IHRoaXMuZ2V0RmlsZU5hbWVGcm9tVXJsKGZpbGVzKSwNCiAgICAgICAgICB1cmw6IGZpbGVzLA0KICAgICAgICAgIHVpZDogYCR7ZmllbGQubmFtZX0tMGAsDQogICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgfV07DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYr+aVsOe7hOagvOW8j++8iOWFvOWuueaXp+aVsOaNru+8iQ0KICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmlsZXMpKSB7DQogICAgICAgIHJldHVybiBmaWxlcy5tYXAoKGZpbGUsIGluZGV4KSA9PiAoew0KICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSB8fCB0aGlzLmdldEZpbGVOYW1lRnJvbVVybChmaWxlLnVybCB8fCBmaWxlKSwNCiAgICAgICAgICB1cmw6IGZpbGUudXJsIHx8IGZpbGUsDQogICAgICAgICAgdWlkOiBgJHtmaWVsZC5uYW1lfS0ke2luZGV4fWAsDQogICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgfSkpOw0KICAgICAgfQ0KDQogICAgICAvLyDlhbbku5bmg4XlhrXov5Tlm57nqbrmlbDnu4QNCiAgICAgIGNvbnNvbGUubG9nKCdnZXRGaWxlTGlzdCAtIOaXoOacieaViOaWh+S7tuaVsOaNru+8jOi/lOWbnuepuuaVsOe7hCcpOw0KICAgICAgcmV0dXJuIFtdOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5bey5LiK5Lyg55qE5paH5Lu25YiX6KGo77yI55So5LqO5pi+56S677yJICovDQogICAgZ2V0VXBsb2FkZWRGaWxlcyhmaWVsZCkgew0KICAgICAgY29uc3QgZmlsZXMgPSBmaWVsZC52YWx1ZTsNCiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KGZpbGVzKSA/IGZpbGVzIDogW107DQogICAgfSwNCg0KICAgIC8qKiDmlofku7bpooTop4ggKi8NCiAgICBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7DQogICAgICBpZiAoZmlsZS51cmwpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwsICdfYmxhbmsnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOS4i+i9veaWh+S7tiAqLw0KICAgIGRvd25sb2FkRmlsZSh1cmwsIGZpbGVOYW1lKSB7DQogICAgICAvLyDliJvlu7rkuIDkuKrkuLTml7bnmoRh5qCH562+5p2l6Kem5Y+R5LiL6L29DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lIHx8ICfkuIvovb3mlofku7YnOw0KICAgICAgbGluay50YXJnZXQgPSAnX2JsYW5rJzsNCiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5bey5LiK5Lyg55qE5paH5Lu2ICovDQogICAgcmVtb3ZlVXBsb2FkZWRGaWxlKGZpZWxkLCBpbmRleCkgew0KICAgICAgaWYgKGZpZWxkLnZhbHVlICYmIEFycmF5LmlzQXJyYXkoZmllbGQudmFsdWUpKSB7DQogICAgICAgIGNvbnN0IG5ld1ZhbHVlID0gWy4uLmZpZWxkLnZhbHVlXTsNCiAgICAgICAgbmV3VmFsdWUuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgdGhpcy5oYW5kbGVGaWVsZElucHV0KGZpZWxkLCBuZXdWYWx1ZSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmlofku7ZVUkwgKi8NCiAgICByZW1vdmVGaWxlVXJsKGZpZWxkKSB7DQogICAgICB0aGlzLmhhbmRsZUZpZWxkSW5wdXQoZmllbGQsICcnKTsNCiAgICB9LA0KDQogICAgLyoqIOS7jlVSTOS4reaPkOWPluaWh+S7tuWQjSAqLw0KICAgIGdldEZpbGVOYW1lRnJvbVVybCh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gJ+acquefpeaWh+S7tic7DQogICAgICBjb25zdCBwYXJ0cyA9IHVybC5zcGxpdCgnLycpOw0KICAgICAgY29uc3QgZmlsZU5hbWUgPSBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXTsNCiAgICAgIC8vIOWmguaenOaWh+S7tuWQjeWMheWQq+aXtumXtOaIs+etie+8jOWwneivleaPkOWPluWOn+Wni+aWh+S7tuWQjQ0KICAgICAgY29uc3QgbWF0Y2ggPSBmaWxlTmFtZS5tYXRjaCgvLipfXGQrQVxkK1wuKC4qKS8pOw0KICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgIHJldHVybiBg5paH5Lu2LiR7bWF0Y2hbMV19YDsNCiAgICAgIH0NCiAgICAgIHJldHVybiBmaWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JzsNCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhuWtl+autei+k+WFpSAqLw0KICAgIGhhbmRsZUZpZWxkSW5wdXQoZmllbGQsIHZhbHVlKSB7DQogICAgICAvLyDmm7TmlrDlrZfmrrXnmoR2YWx1ZQ0KICAgICAgZmllbGQudmFsdWUgPSB2YWx1ZTsNCiAgICAgIC8vIOWQjOatpeWIsOihqOWNleaVsOaNrg0KICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgdmFsdWUpOw0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUZpZWxkSW5wdXQgLSBmaWVsZDonLCBmaWVsZC5uYW1lLCAndmFsdWU6JywgdmFsdWUpOw0KICAgIH0sDQoNCiAgICAvKiog5pu05paw5a2X5q615YC85Yiw6KGo5Y2V5pWw5o2uICovDQogICAgdXBkYXRlRmllbGRWYWx1ZShmaWVsZCkgew0KICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmllbGQudmFsdWUpOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5YiG57G75ZCN56ewICovDQogICAgZ2V0Q2F0ZWdvcnlOYW1lKCkgew0KICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhWzBdLm5hbWUgfHwgJ+S4k+WxnuWtl+autSc7DQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZENhdGVnb3J5TmFtZSB8fCAn5LiT5bGe5a2X5q61JzsNCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhuWIhuexu+Wtl+auteaVsOaNriAqLw0KICAgIHByb2Nlc3NDYXRlZ29yeUZpZWxkc0RhdGEoZGF0YSkgew0KICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhID0gSlNPTi5wYXJzZShkYXRhKTsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWIhuexu+Wtl+auteaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSA9IFtdOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEgPSBkYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEgPSBbXTsNCiAgICAgIH0NCg0KICAgICAgLy8g5Yid5aeL5YyW5a2X5q615YC85Yiw6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgIGlmIChjYXRlZ29yeURhdGEuZmllbGRzKSB7DQogICAgICAgICAgY2F0ZWdvcnlEYXRhLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIC8vIOehruS/neWtl+auteacieWIneWni+WAvA0KICAgICAgICAgICAgaWYgKGZpZWxkLnZhbHVlID09PSB1bmRlZmluZWQgfHwgZmllbGQudmFsdWUgPT09IG51bGwpIHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdmaWxlJykgew0KICAgICAgICAgICAgICAgIGZpZWxkLnZhbHVlID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94Jykgew0KICAgICAgICAgICAgICAgIGZpZWxkLnZhbHVlID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgZmllbGQudmFsdWUgPSAnJzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDku47ooajljZXmlbDmja7kuK3mgaLlpI3lrZfmrrXlgLzvvIjlpoLmnpzlrZjlnKjvvIkNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uZHluYW1pY0RhdGEgJiYgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZmllbGQudmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV07DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAvLyDorr7nva7liLDooajljZXmlbDmja4NCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmllbGQudmFsdWUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleaWsOeahOaVsOaNruagvOW8jyAqLw0KICAgIHRlc3ROZXdEYXRhRm9ybWF0KCkgew0KICAgICAgLy8g5L2/55So5oKo5o+Q5L6b55qE5a6e6ZmFSlNPTuaVsOaNruagvOW8j+i/m+ihjOa1i+ivlQ0KICAgICAgY29uc3QgdGVzdERhdGEgPSBbDQogICAgICAgIHsNCiAgICAgICAgICAibmFtZSI6ICLln7rnoYDkv6Hmga8iLA0KICAgICAgICAgICJkZXNjcmlwdGlvbiI6ICIiLA0KICAgICAgICAgICJmaWVsZHMiOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgICJsYWJlbCI6ICLkvIHkuJrlhajnp7AiLA0KICAgICAgICAgICAgICAibmFtZSI6ICJmaWVsZF82NTI0MDgiLA0KICAgICAgICAgICAgICAidHlwZSI6ICJpbnB1dCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICIiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAi5rWL6K+V5LyB5Lia5pyJ6ZmQ5YWs5Y+4Ig0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgImxhYmVsIjogIuihjOS4muagh+etviIsDQogICAgICAgICAgICAgICJuYW1lIjogImZpZWxkXzcyMDk0NCIsDQogICAgICAgICAgICAgICJ0eXBlIjogInNlbGVjdCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIuaWsOiDvea6kCznoaznp5HmioAiLA0KICAgICAgICAgICAgICAicGxhY2Vob2xkZXIiOiAi6K+36YCJ5oupIiwNCiAgICAgICAgICAgICAgInN0YXRpY0NvbnRlbnQiOiAiIiwNCiAgICAgICAgICAgICAgInZhbHVlIjogIuaWsOiDvea6kCINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgICJsYWJlbCI6ICLogZTns7vkuroiLA0KICAgICAgICAgICAgICAibmFtZSI6ICJjb250YWN0X25hbWUiLA0KICAgICAgICAgICAgICAidHlwZSI6ICJpbnB1dCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICIiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAi5byg5LiJIg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgImxhYmVsIjogIueUteivnSIsDQogICAgICAgICAgICAgICJuYW1lIjogInBob25lIiwNCiAgICAgICAgICAgICAgInR5cGUiOiAidGVsIiwNCiAgICAgICAgICAgICAgInJlcXVpcmVkIjogdHJ1ZSwNCiAgICAgICAgICAgICAgIm9wdGlvbnMiOiAiIiwNCiAgICAgICAgICAgICAgInBsYWNlaG9sZGVyIjogIuivt+i+k+WFpSIsDQogICAgICAgICAgICAgICJzdGF0aWNDb250ZW50IjogIiIsDQogICAgICAgICAgICAgICJ2YWx1ZSI6ICIxMzgwMDEzODAwMCINCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgICJpY29uIjogImh0dHA6Ly8xOTIuMTY4LjIuODM6ODA4MC9wcm9maWxlL3VwbG9hZC8yMDI1LzA3LzIyLzIwMjUwNzIyLTEwMDIyOF8yMDI1MDcyMjEwMDQwM0EwMDQucG5nIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgIm5hbWUiOiAi5YW25LuW5p2Q5paZ6KGl5YWFIiwNCiAgICAgICAgICAiZGVzY3JpcHRpb24iOiAiIiwNCiAgICAgICAgICAiZmllbGRzIjogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICAibGFiZWwiOiAi5LiK5Lyg6ZmE5Lu2IiwNCiAgICAgICAgICAgICAgIm5hbWUiOiAiZmllbGRfOTg5MjIyIiwNCiAgICAgICAgICAgICAgInR5cGUiOiAiZmlsZSIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IGZhbHNlLA0KICAgICAgICAgICAgICAib3B0aW9ucyI6ICIiLA0KICAgICAgICAgICAgICAicGxhY2Vob2xkZXIiOiAi5pyq6YCJ5oup5Lu75L2V5paH5Lu2IiwNCiAgICAgICAgICAgICAgInN0YXRpY0NvbnRlbnQiOiAiIiwNCiAgICAgICAgICAgICAgInZhbHVlIjogImh0dHA6Ly8xOTIuMTY4LjIuODM6ODA4MC9wcm9maWxlL3VwbG9hZC8yMDI1LzA3LzIzL3hodUZ3YTBxdWxQUzAzOTExYzM1MzI5ZjY5NTg0OGZiNjU5YTI0ZjZmMTU5XzIwMjUwNzIzMTgzMjIwQTAwMS5wbmciDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICAibGFiZWwiOiAi6YKu5Lu25o+Q5Lqk6IezIiwNCiAgICAgICAgICAgICAgIm5hbWUiOiAiZmllbGRfMjI3OTY5IiwNCiAgICAgICAgICAgICAgInR5cGUiOiAic3RhdGljIiwNCiAgICAgICAgICAgICAgInJlcXVpcmVkIjogZmFsc2UsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICJ5YW5neHVleXVlQGh0Y3lzdC5jb20o5paH5Lu25ZCN77ya44CQ5LyB5Lia5pud5YWJ55Sz6K+344CRLeS8geS4mi/pobnnm67lkI3vvIkiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAiIg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgImljb24iOiAiaHR0cDovLzE5Mi4xNjguMi44Mzo4MDgwL3Byb2ZpbGUvdXBsb2FkLzIwMjUvMDcvMjIvMjAyNTA3MjItMTAwMjQ3XzIwMjUwNzIyMTAwNDU5QTAwOS5wbmciDQogICAgICAgIH0NCiAgICAgIF07DQoNCiAgICAgIC8vIOW9k+eCueWHu+S/ruaUueaMiemSruaXtu+8jOWPr+S7peiwg+eUqOi/meS4quaWueazleadpeiuvue9rua1i+ivleaVsOaNrg0KICAgICAgLy8gdGhpcy5wcm9jZXNzQ2F0ZWdvcnlGaWVsZHNEYXRhKHRlc3REYXRhKTsNCiAgICB9LA0KDQoNCg0KDQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAogCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;AAKA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/business/demand", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n        <el-input\r\n          v-model=\"queryParams.demandTitle\"\r\n          placeholder=\"请输入需求标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n        <el-select v-model=\"queryParams.categoryId\" placeholder=\"请选择需求类型\" clearable>\r\n          <el-option\r\n            v-for=\"category in categoryList\"\r\n            :key=\"category.categoryId\"\r\n            :label=\"category.categoryName\"\r\n            :value=\"category.categoryId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n        <el-select v-model=\"queryParams.demandStatus\" placeholder=\"请选择需求状态\" clearable>\r\n          <el-option label=\"已发布\" value=\"0\" />\r\n          <el-option label=\"已对接\" value=\"1\" />\r\n          <el-option label=\"已下架\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"对接状态\" prop=\"hasDocking\">\r\n        <el-select v-model=\"queryParams.hasDocking\" placeholder=\"请选择对接状态\" clearable>\r\n          <el-option label=\"未对接\" value=\"0\" />\r\n          <el-option label=\"对接中\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"时间筛选\" prop=\"timeFilter\">\r\n        <el-select v-model=\"queryParams.timeFilter\" placeholder=\"请选择时间范围\" clearable>\r\n          <el-option label=\"一周内发布\" value=\"week_within\" />\r\n          <el-option label=\"发布满一周\" value=\"week_over\" />\r\n          <el-option label=\"发布满一月\" value=\"month_over\" />\r\n          <el-option label=\"发布满一年\" value=\"year_over\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:demand:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:demand:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:demand:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:demand:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"demandList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n      <el-table-column label=\"需求ID\" align=\"center\" prop=\"demandId\" width=\"70\" />\r\n      <el-table-column label=\"需求标题\" align=\"left\" prop=\"demandTitle\" min-width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"需求类型\" align=\"center\" prop=\"categoryName\" width=\"90\" />\r\n      <el-table-column label=\"需求状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n          <el-tag v-else-if=\"scope.row.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n          <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"对接状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.hasDocking === true || scope.row.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n          <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否置顶\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.isTop === '1'\" type=\"warning\" size=\"mini\">置顶</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"320\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"table-actions\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDetail(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:query']\"\r\n            >详情</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >修改</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:remove']\"\r\n            >删除</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleToggleTop(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >{{ scope.row.isTop === '1' ? '取消置顶' : '置顶' }}</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus !== '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOffShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #E6A23C;\"\r\n            >下架</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus === '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOnShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #67C23A;\"\r\n            >上架</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 联系记录弹窗 -->\r\n    <el-dialog title=\"联系记录\" :visible.sync=\"contactDialogVisible\" width=\"50%\" append-to-body>\r\n      <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactRules\" label-width=\"100px\">\r\n        <el-form-item label=\"对接用户\">\r\n          <span>{{ contactForm.userName }} ({{ contactForm.userPhone }})</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否已联系\" prop=\"isContacted\">\r\n          <el-radio-group v-model=\"contactForm.isContacted\">\r\n            <el-radio label=\"0\">未联系</el-radio>\r\n            <el-radio label=\"1\">已联系</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系结果\" prop=\"contactResult\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-select v-model=\"contactForm.contactResult\" placeholder=\"请选择联系结果\" clearable style=\"width: 100%;\">\r\n            <el-option label=\"联系成功\" value=\"联系成功\" />\r\n            <el-option label=\"无人接听\" value=\"无人接听\" />\r\n            <el-option label=\"号码错误\" value=\"号码错误\" />\r\n            <el-option label=\"拒绝沟通\" value=\"拒绝沟通\" />\r\n            <el-option label=\"稍后联系\" value=\"稍后联系\" />\r\n            <el-option label=\"已有合作\" value=\"已有合作\" />\r\n            <el-option label=\"不感兴趣\" value=\"不感兴趣\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系备注\" prop=\"contactNotes\">\r\n          <el-input\r\n            v-model=\"contactForm.contactNotes\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入联系备注，如沟通内容、后续计划等\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系时间\" prop=\"contactTime\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-date-picker\r\n            v-model=\"contactForm.contactTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择联系时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%;\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"contactDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitContactForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 需求详情弹窗 -->\r\n    <el-dialog title=\"需求详情\" :visible.sync=\"detailDialogVisible\" width=\"70%\" append-to-body>\r\n      <div class=\"detail-content\">\r\n        <!-- 内容信息 -->\r\n        <div class=\"info-section\">\r\n          <h4 class=\"section-header\">内容信息</h4>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">标题：</span>\r\n            <span class=\"info-value\">{{ detailForm.demandTitle || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">类型：</span>\r\n            <span class=\"info-value\">{{ detailForm.categoryName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n              <el-tag v-else-if=\"detailForm.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n              <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">对接状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.hasDocking === true || detailForm.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n              <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">是否置顶：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.isTop === '1'\" type=\"warning\" size=\"small\">置顶</el-tag>\r\n              <span v-else>否</span>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">浏览次数：</span>\r\n            <span class=\"info-value\">{{ detailForm.viewCount || 0 }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">发布时间：</span>\r\n            <span class=\"info-value\">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\" v-if=\"detailForm.demandDesc\">\r\n            <span class=\"info-label\">需求描述：</span>\r\n            <div class=\"info-value description-text\">{{ detailForm.demandDesc }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系人：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系电话：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactPhone || '' }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          表单数据\r\n        </el-divider>\r\n\r\n        <!-- 表单数据部分 -->\r\n        <div class=\"form-data-section\" v-if=\"detailForm.formDataList && detailForm.formDataList.length > 0\">\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item\r\n              v-for=\"(item, index) in detailForm.formDataList\"\r\n              :key=\"index\"\r\n              :label=\"item.label\"\r\n              :label-style=\"{ width: '120px', fontWeight: 'bold' }\"\r\n            >\r\n              <template v-if=\"item.type === 'textarea'\">\r\n                <div class=\"textarea-content\">{{ item.value || '未填写' }}</div>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'select' || item.type === 'radio'\">\r\n                <el-tag type=\"primary\" size=\"small\">{{ item.value || '未选择' }}</el-tag>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'checkbox'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <el-tag v-for=\"val in item.value\" :key=\"val\" type=\"primary\" size=\"small\" style=\"margin-right: 5px;\">{{ val }}</el-tag>\r\n                </div>\r\n                <span v-else>未选择</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'file'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <div v-for=\"(file, fileIndex) in item.value\" :key=\"fileIndex\" class=\"file-item\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <a :href=\"file.url || file\" target=\"_blank\" class=\"file-link\">\r\n                      {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n                <span v-else-if=\"typeof item.value === 'string' && item.value\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <a :href=\"item.value\" target=\"_blank\" class=\"file-link\">\r\n                    {{ getFileNameFromUrl(item.value) }}\r\n                  </a>\r\n                </span>\r\n                <span v-else>未上传</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'tel'\">\r\n                <span class=\"phone-number\">{{ item.value || '未填写' }}</span>\r\n              </template>\r\n              <template v-else>\r\n                <span>{{ item.value || '未填写' }}</span>\r\n              </template>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          对接记录\r\n          <el-tag size=\"small\" type=\"info\" style=\"margin-left: 8px;\">\r\n            共 {{ (detailForm.dockingList || []).length }} 条记录\r\n          </el-tag>\r\n        </el-divider>\r\n\r\n        <!-- 对接记录部分 -->\r\n        <div class=\"docking-section\">\r\n          <el-table\r\n            :data=\"detailForm.dockingList || []\"\r\n            size=\"small\"\r\n            border\r\n            :key=\"'docking-table-' + tableRefreshKey\"\r\n            class=\"docking-table\"\r\n            v-loading=\"detailForm.dockingLoading\"\r\n          >\r\n            <!-- 自定义空数据提示 -->\r\n            <template slot=\"empty\">\r\n              <div class=\"empty-data\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <p>暂无对接记录</p>\r\n              </div>\r\n            </template>\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n\r\n              <el-table-column label=\"对接用户\" width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"user-info\">\r\n                    <div class=\"user-name\">\r\n                      <i class=\"el-icon-user\"></i>\r\n                      <strong>{{ scope.row.userName || '未知用户' }}</strong>\r\n                    </div>\r\n                    <div class=\"user-phone\" v-if=\"scope.row.userPhone\">\r\n                      <i class=\"el-icon-phone\"></i>\r\n                      {{ scope.row.userPhone }}\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"工作信息\" min-width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"work-info\">\r\n                    <div class=\"company\" v-if=\"scope.row.userCompany\">\r\n                      <i class=\"el-icon-office-building\"></i>\r\n                      {{ scope.row.userCompany }}\r\n                    </div>\r\n                    <div class=\"position\" v-if=\"scope.row.userPosition\">\r\n                      <i class=\"el-icon-suitcase\"></i>\r\n                      {{ scope.row.userPosition }}\r\n                    </div>\r\n                    <div v-if=\"!scope.row.userCompany && !scope.row.userPosition\" class=\"no-info\">\r\n                      暂无工作信息\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"对接时间\" width=\"140\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"docking-time\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    {{ parseTime(scope.row.dockingTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"联系状态\" width=\"100\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag\r\n                    :type=\"(scope.row.isContacted === '1' || scope.row.isContacted === 1) ? 'success' : 'warning'\"\r\n                    size=\"small\"\r\n                  >\r\n                    {{ (scope.row.isContacted === '1' || scope.row.isContacted === 1) ? '已联系' : '未联系' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-edit-outline\"\r\n                    @click=\"handleContactRecord(scope.row)\"\r\n                  >\r\n                    联系记录\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改需求对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n              <el-select v-model=\"form.categoryId\" placeholder=\"请选择需求类型\" style=\"width: 100%\" @change=\"onCategoryChange\">\r\n                <el-option\r\n                  v-for=\"category in categoryList\"\r\n                  :key=\"category.categoryId\"\r\n                  :label=\"category.categoryName\"\r\n                  :value=\"category.categoryId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n              <el-select v-model=\"form.demandStatus\" placeholder=\"请选择需求状态\" style=\"width: 100%\">\r\n                <el-option label=\"已发布\" value=\"0\" />\r\n                <el-option label=\"已对接\" value=\"1\" />\r\n                <el-option label=\"已下架\" value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n          <el-input v-model=\"form.demandTitle\" placeholder=\"请输入需求标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"需求描述\" prop=\"demandDesc\">\r\n          <el-input v-model=\"form.demandDesc\" type=\"textarea\" placeholder=\"请输入需求描述\" :rows=\"4\" />\r\n        </el-form-item>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人姓名\" prop=\"contactName\">\r\n              <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人姓名\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人电话\" prop=\"contactPhone\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入联系人电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否置顶\" prop=\"isTop\">\r\n              <el-radio-group v-model=\"form.isTop\">\r\n                <el-radio label=\"0\">否</el-radio>\r\n                <el-radio label=\"1\">是</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 动态表单字段 -->\r\n        <div v-if=\"categoryFieldsData && categoryFieldsData.length > 0\" class=\"dynamic-fields-section\">\r\n          <el-divider content-position=\"left\">\r\n            <span style=\"color: #409EFF; font-weight: bold;\">{{ getCategoryName() }}专属字段</span>\r\n          </el-divider>\r\n\r\n          <!-- 渲染分类字段 -->\r\n          <div v-for=\"(categoryData, categoryIndex) in categoryFieldsData\" :key=\"`category-${categoryIndex}`\" class=\"category-group\">\r\n            <div v-if=\"categoryData.name\" class=\"category-title\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n              <span>{{ categoryData.name }}</span>\r\n            </div>\r\n            <div v-if=\"categoryData.description\" class=\"category-description\">\r\n              {{ categoryData.description }}\r\n            </div>\r\n\r\n            <div v-for=\"(field, fieldIndex) in categoryData.fields\" :key=\"`field-${field.name}-${fieldIndex}`\" class=\"dynamic-field-item\">\r\n              <div class=\"field-label\">\r\n                <span v-if=\"field.required\" class=\"required-mark\">*</span>\r\n                {{ field.label }}\r\n              </div>\r\n              <div class=\"field-content\">\r\n                <!-- 静态内容 -->\r\n                <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                  {{ field.staticContent }}\r\n                </div>\r\n                <!-- 文本输入 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'input'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 多行文本 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'textarea'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  type=\"textarea\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  :rows=\"3\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 数字输入 -->\r\n                <el-input-number\r\n                  v-else-if=\"field.type === 'number'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || 0\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 电话号码 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'tel'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 邮箱地址 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'email'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 单选框 -->\r\n                <el-radio-group\r\n                  v-else-if=\"field.type === 'radio'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-radio\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-radio-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                </el-radio-group>\r\n                <!-- 多选框 -->\r\n                <el-checkbox-group\r\n                  v-else-if=\"field.type === 'checkbox'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || []\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-checkbox\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-checkbox-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                </el-checkbox-group>\r\n                <!-- 下拉选择 -->\r\n                <el-select\r\n                  v-else-if=\"field.type === 'select'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-option-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                </el-select>\r\n                <!-- 日期选择 -->\r\n                <el-date-picker\r\n                  v-else-if=\"field.type === 'date'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  type=\"date\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 时间选择 -->\r\n                <el-time-picker\r\n                  v-else-if=\"field.type === 'time'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 文件上传 -->\r\n                <div v-else-if=\"field.type === 'file'\">\r\n                  <!-- 如果已有文件URL，显示文件信息 -->\r\n                  <div v-if=\"field.value && typeof field.value === 'string' && field.value.startsWith('http')\" class=\"existing-file\">\r\n                    <div class=\"file-display\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a :href=\"field.value\" target=\"_blank\" class=\"file-link\">\r\n                        {{ getFileNameFromUrl(field.value) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeFileUrl(field)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 文件上传组件 -->\r\n                  <el-upload\r\n                    v-else\r\n                    action=\"/dev-api/common/upload\"\r\n                    :headers=\"uploadHeaders\"\r\n                    :on-success=\"(response, file, fileList) => handleFileSuccess(response, file, fileList, field)\"\r\n                    :on-remove=\"(file, fileList) => handleFileRemove(file, fileList, field)\"\r\n                    :file-list=\"getFileList(field)\"\r\n                    :on-preview=\"handleFilePreview\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n                  </el-upload>\r\n\r\n                  <!-- 已上传文件列表显示（数组格式） -->\r\n                  <div v-if=\"Array.isArray(field.value) && field.value.length > 0\" class=\"uploaded-files-list\">\r\n                    <div class=\"uploaded-files-title\">已上传文件：</div>\r\n                    <div\r\n                      v-for=\"(file, index) in field.value\"\r\n                      :key=\"`uploaded-${field.name}-${index}`\"\r\n                      class=\"uploaded-file-item\"\r\n                    >\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a\r\n                        :href=\"file.url || file\"\r\n                        target=\"_blank\"\r\n                        class=\"file-link\"\r\n                        @click=\"downloadFile(file.url || file, file.name || getFileNameFromUrl(file))\"\r\n                      >\r\n                        {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeUploadedFile(field, index)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n/* 详情页面样式 */\r\n.detail-content {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.info-section, .form-data-section, .docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  color: #303133;\r\n}\r\n\r\n.description-text {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.6;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-link {\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n.file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-section .el-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n/* 空数据提示样式 */\r\n.empty-data {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  color: #C0C4CC;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-section .user-name {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.docking-section .user-phone {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n  margin-right: 5px;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 表格操作列样式优化 */\r\n.table-actions {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n  min-width: auto;\r\n}\r\n\r\n.table-actions .el-button + .el-button {\r\n  margin-left: 2px;\r\n}\r\n\r\n.el-table .small-padding .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n  white-space: nowrap;\r\n  overflow: visible;\r\n}\r\n\r\n.el-table .fixed-width .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n}\r\n\r\n/* 表格列间距优化 */\r\n.el-table th,\r\n.el-table td {\r\n  padding: 6px 0;\r\n}\r\n\r\n.el-table .cell {\r\n  padding-left: 6px;\r\n  padding-right: 6px;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n.docking-table .user-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .user-name {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-name i {\r\n  margin-right: 4px;\r\n  color: #409EFF;\r\n}\r\n\r\n.docking-table .user-phone {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-phone i {\r\n  margin-right: 4px;\r\n  color: #67C23A;\r\n}\r\n\r\n.docking-table .work-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .company,\r\n.docking-table .position {\r\n  margin-bottom: 4px;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .company i,\r\n.docking-table .position i {\r\n  margin-right: 4px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-table .no-info {\r\n  color: #C0C4CC;\r\n  font-size: 12px;\r\n  font-style: italic;\r\n}\r\n\r\n.docking-table .docking-time {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.docking-table .docking-time i {\r\n  margin-right: 4px;\r\n  color: #E6A23C;\r\n}\r\n\r\n/* 表格行样式 */\r\n.docking-table .el-table__row {\r\n  cursor: default;\r\n}\r\n\r\n.docking-table .el-table__row:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listDemand, getDemand, delDemand, addDemand, updateDemand, offShelfDemand, onShelfDemand, updateContactStatus } from \"@/api/miniapp/demand\";\r\nimport { getEnabledDemandCategoryList } from \"@/api/miniapp/demandcategory\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"MiniDemand\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 需求表格数据\r\n      demandList: [],\r\n      // 联系记录弹窗\r\n      contactDialogVisible: false,\r\n      // 联系记录表单\r\n      contactForm: {\r\n        dockingId: null,\r\n        userName: '',\r\n        userPhone: '',\r\n        isContacted: '0',\r\n        contactResult: '',\r\n        contactNotes: '',\r\n        contactTime: ''\r\n      },\r\n      // 联系记录表单验证\r\n      contactRules: {\r\n        isContacted: [\r\n          { required: true, message: \"请选择是否已联系\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 详情弹窗\r\n      detailDialogVisible: false,\r\n      // 详情数据\r\n      detailForm: {\r\n        dockingList: [],\r\n        formDataList: []\r\n      },\r\n      // 表格刷新key\r\n      tableRefreshKey: 0,\r\n      // 需求类型列表\r\n      categoryList: [],\r\n      // 动态表单字段\r\n      dynamicFields: [],\r\n      // 选中的类型名称\r\n      selectedCategoryName: '',\r\n      // 分类字段数据（新格式）\r\n      categoryFieldsData: [],\r\n      // 上传请求头\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + this.$store.getters.token\r\n      },\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        demandTitle: null,\r\n        categoryId: null,\r\n        demandStatus: null,\r\n        hasDocking: null,\r\n        timeFilter: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        categoryId: [\r\n          { required: true, message: \"需求类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" }\r\n        ],\r\n        demandDesc: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactName: [\r\n          { required: true, message: \"联系人姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系人电话不能为空\", trigger: \"blur\" },\r\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        demandStatus: [\r\n          { required: true, message: \"需求状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    /** 按模块分组的动态字段 */\r\n    groupedDynamicFields() {\r\n      const grouped = {};\r\n      this.dynamicFields.forEach(field => {\r\n        const moduleTitle = field.moduleTitle || '其他字段';\r\n        if (!grouped[moduleTitle]) {\r\n          grouped[moduleTitle] = [];\r\n        }\r\n        grouped[moduleTitle].push(field);\r\n      });\r\n      return grouped;\r\n    },\r\n\r\n    /** 安全的动态数据访问器 */\r\n    safeDynamicData() {\r\n      const safeData = { ...this.form.dynamicData };\r\n      this.dynamicFields.forEach(field => {\r\n        if (field.name) {\r\n          if (field.type === 'checkbox' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          } else if (field.type === 'file' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          }\r\n        }\r\n      });\r\n      return safeData;\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCategoryList();\r\n    // 测试新的数据格式\r\n    this.testNewDataFormat();\r\n  },\r\n  methods: {\r\n    /** 查询需求列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDemand(this.queryParams).then(response => {\r\n        this.demandList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取需求列表失败:', error);\r\n        this.loading = false;\r\n        this.$modal.msgError(\"获取需求列表失败\");\r\n      });\r\n    },\r\n    /** 获取需求类型列表 */\r\n    getCategoryList() {\r\n      getEnabledDemandCategoryList().then(response => {\r\n        this.categoryList = response.data;\r\n      }).catch(error => {\r\n        console.error('获取需求类型列表失败:', error);\r\n        this.$modal.msgError(\"获取需求类型列表失败\");\r\n      });\r\n    },\r\n\r\n\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        demandId: null,\r\n        categoryId: null,\r\n        demandTitle: \"\",\r\n        demandDesc: \"\",\r\n        contactName: \"\",\r\n        contactPhone: \"\",\r\n        demandStatus: \"0\",\r\n        isTop: \"0\",\r\n        remark: \"\",\r\n        dynamicData: {}\r\n      };\r\n\r\n      // 清除动态字段的验证规则\r\n      Object.keys(this.rules).forEach(key => {\r\n        if (key.startsWith('dynamicData.')) {\r\n          this.$delete(this.rules, key);\r\n        }\r\n      });\r\n\r\n      // 重置动态字段\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      this.resetForm(\"form\");\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.demandId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加需求\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // 先清理状态，但不重置表单\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      const demandId = row.demandId || this.ids;\r\n      getDemand(demandId).then(response => {\r\n        // 使用$set来保持响应式\r\n        const data = response.data;\r\n        this.$set(this.form, 'demandId', data.demandId);\r\n        this.$set(this.form, 'categoryId', data.categoryId);\r\n        this.$set(this.form, 'demandTitle', data.demandTitle || \"\");\r\n        this.$set(this.form, 'demandDesc', data.demandDesc || \"\");\r\n        this.$set(this.form, 'contactName', data.contactName || \"\");\r\n        this.$set(this.form, 'contactPhone', data.contactPhone || \"\");\r\n        this.$set(this.form, 'demandStatus', data.demandStatus || \"0\");\r\n        this.$set(this.form, 'isTop', data.isTop || \"0\");\r\n        this.$set(this.form, 'remark', data.remark || \"\");\r\n\r\n        // 解析动态表单数据\r\n        if (data.formData) {\r\n          try {\r\n            const formData = JSON.parse(data.formData);\r\n\r\n            // 检查是否是新格式的数据（包含fields数组的对象）\r\n            if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n              // 新格式：先设置表单数据，再处理分类字段数据\r\n              this.$set(this.form, 'dynamicData', {});\r\n\r\n              // 从fields中提取数据到dynamicData\r\n              formData.forEach(categoryData => {\r\n                if (categoryData.fields) {\r\n                  categoryData.fields.forEach(field => {\r\n                    if (field.value !== undefined && field.value !== null && field.value !== '') {\r\n                      this.$set(this.form.dynamicData, field.name, field.value);\r\n                    }\r\n                  });\r\n                }\r\n              });\r\n\r\n              // 处理分类字段数据\r\n              this.processCategoryFieldsData(formData);\r\n            } else {\r\n              // 旧格式：直接使用formData作为dynamicData\r\n              this.$set(this.form, 'dynamicData', formData);\r\n              this.loadDynamicFields(this.form.categoryId);\r\n            }\r\n          } catch (e) {\r\n            console.error('解析动态表单数据失败:', e);\r\n            this.$set(this.form, 'dynamicData', {});\r\n            this.loadDynamicFields(this.form.categoryId);\r\n          }\r\n        } else {\r\n          this.$set(this.form, 'dynamicData', {});\r\n          this.loadDynamicFields(this.form.categoryId);\r\n        }\r\n\r\n        // 在下一个tick中清除表单验证状态\r\n        this.$nextTick(() => {\r\n          if (this.$refs.form) {\r\n            this.$refs.form.clearValidate();\r\n          }\r\n        });\r\n\r\n        this.open = true;\r\n        this.title = \"修改需求\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 先验证动态字段\r\n      let dynamicFieldsValid = true;\r\n      let firstErrorField = null;\r\n\r\n      // 验证新格式的分类字段数据\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        this.categoryFieldsData.forEach(categoryData => {\r\n          if (categoryData.fields) {\r\n            categoryData.fields.forEach(field => {\r\n              if (field.required && field.name && field.type !== 'static') {\r\n                const value = this.form.dynamicData[field.name];\r\n                let isEmpty = false;\r\n\r\n                if (field.type === 'checkbox' || field.type === 'file') {\r\n                  isEmpty = !Array.isArray(value) || value.length === 0;\r\n                } else {\r\n                  isEmpty = value === null || value === undefined || value === '';\r\n                }\r\n\r\n                if (isEmpty) {\r\n                  dynamicFieldsValid = false;\r\n                  if (!firstErrorField) {\r\n                    firstErrorField = field.label;\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        // 验证旧格式的动态字段\r\n        this.dynamicFields.forEach(field => {\r\n          if (field.required && field.name) {\r\n            const value = this.form.dynamicData[field.name];\r\n            let isEmpty = false;\r\n\r\n            if (field.type === 'checkbox' || field.type === 'file') {\r\n              isEmpty = !Array.isArray(value) || value.length === 0;\r\n            } else {\r\n              isEmpty = value === null || value === undefined || value === '';\r\n            }\r\n\r\n            if (isEmpty) {\r\n              dynamicFieldsValid = false;\r\n              if (!firstErrorField) {\r\n                firstErrorField = field.label;\r\n              }\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (!dynamicFieldsValid) {\r\n        this.$modal.msgError(`${firstErrorField}不能为空`);\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const formData = { ...this.form };\r\n\r\n          // 构建包含value的完整字段数据格式\r\n          if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n            // 新格式：使用分类字段数据，并更新每个字段的value\r\n            const categoryDataWithValues = this.categoryFieldsData.map(categoryData => ({\r\n              ...categoryData,\r\n              fields: categoryData.fields.map(field => ({\r\n                ...field,\r\n                value: this.form.dynamicData[field.name] || field.value || (field.type === 'checkbox' || field.type === 'file' ? [] : '')\r\n              }))\r\n            }));\r\n            formData.formData = JSON.stringify(categoryDataWithValues);\r\n          } else if (formData.dynamicData && Object.keys(formData.dynamicData).length > 0) {\r\n            // 旧格式：直接使用dynamicData\r\n            formData.formData = JSON.stringify(formData.dynamicData);\r\n          }\r\n\r\n          delete formData.dynamicData; // 删除临时字段\r\n\r\n          console.log('submitForm - formData.formData:', formData.formData);\r\n\r\n          if (this.form.demandId != null) {\r\n            updateDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 查看详情 */\r\n    handleDetail(row) {\r\n      console.log('查看详情 - 原始数据:', row);\r\n\r\n      // 使用Vue.set确保响应式\r\n      this.$set(this, 'detailForm', {\r\n        ...row,\r\n        dockingLoading: true,\r\n        dockingList: [],\r\n        formDataList: []\r\n      });\r\n\r\n      // 解析表单数据\r\n      if (row.formData) {\r\n        try {\r\n          const formData = JSON.parse(row.formData);\r\n          this.$set(this.detailForm, 'formDataList', this.parseFormDataForDisplay(formData));\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n          this.$set(this.detailForm, 'formDataList', []);\r\n        }\r\n      }\r\n\r\n      console.log('详情表单数据:', this.detailForm);\r\n\r\n      // 打开弹窗\r\n      this.detailDialogVisible = true;\r\n\r\n      // 加载对接记录\r\n      request({\r\n        url: `/miniapp/demand/${row.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 联系记录操作 */\r\n    handleContactRecord(dockingRow) {\r\n      console.log('打开联系记录弹窗，对接记录数据:', dockingRow);\r\n      this.contactForm = {\r\n        dockingId: dockingRow.dockingId,\r\n        userName: dockingRow.userName,\r\n        userPhone: dockingRow.userPhone,\r\n        isContacted: dockingRow.isContacted || '0',\r\n        contactResult: dockingRow.contactResult || '',\r\n        contactNotes: dockingRow.contactNotes || '',\r\n        contactTime: dockingRow.contactTime || ''\r\n      };\r\n      console.log('联系表单数据:', this.contactForm);\r\n      this.contactDialogVisible = true;\r\n    },\r\n\r\n    /** 提交联系记录表单 */\r\n    submitContactForm() {\r\n      this.$refs[\"contactForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 如果选择已联系但没有设置联系时间，使用当前时间\r\n          if (this.contactForm.isContacted === '1' && !this.contactForm.contactTime) {\r\n            this.contactForm.contactTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');\r\n          }\r\n\r\n          // 保存当前联系状态，用于直接更新本地数据\r\n          const dockingId = this.contactForm.dockingId;\r\n          const newIsContacted = this.contactForm.isContacted;\r\n          const newContactResult = this.contactForm.contactResult;\r\n          const newContactNotes = this.contactForm.contactNotes;\r\n          const newContactTime = this.contactForm.contactTime;\r\n\r\n          updateContactStatus(this.contactForm).then((response) => {\r\n            console.log('联系状态更新成功:', response);\r\n            this.$modal.msgSuccess(\"联系记录更新成功\");\r\n            this.contactDialogVisible = false;\r\n\r\n            // 如果详情弹窗是打开的，先直接更新本地数据，再刷新详情中的对接记录\r\n            if (this.detailDialogVisible && this.detailForm.demandId && this.detailForm.dockingList) {\r\n              console.log('开始更新本地对接记录...');\r\n\r\n              // 先直接更新本地数据，立即反映变化\r\n              const dockingItem = this.detailForm.dockingList.find(item => item.dockingId === dockingId);\r\n              console.log('找到的对接记录:', dockingItem);\r\n              console.log('要更新的联系状态:', newIsContacted);\r\n              console.log('当前dockingList:', this.detailForm.dockingList);\r\n\r\n              if (dockingItem) {\r\n                console.log('更新前的联系状态:', dockingItem.isContacted);\r\n                this.$set(dockingItem, 'isContacted', newIsContacted);\r\n                this.$set(dockingItem, 'contactResult', newContactResult);\r\n                this.$set(dockingItem, 'contactNotes', newContactNotes);\r\n                this.$set(dockingItem, 'contactTime', newContactTime);\r\n                console.log('更新后的联系状态:', dockingItem.isContacted);\r\n\r\n                // 强制刷新表格\r\n                this.tableRefreshKey++;\r\n                console.log('强制刷新表格，新key:', this.tableRefreshKey);\r\n              } else {\r\n                console.error('未找到对应的对接记录，dockingId:', dockingId);\r\n                console.error('所有对接记录的ID:', this.detailForm.dockingList.map(item => item.dockingId));\r\n              }\r\n\r\n              // 然后再从服务器刷新完整数据\r\n              console.log('开始刷新详情中的对接记录...');\r\n              this.refreshDetailDockingList();\r\n            }\r\n\r\n            // 刷新主列表以更新统计数据\r\n            this.getList();\r\n          }).catch((error) => {\r\n            console.error('联系状态更新失败:', error);\r\n            this.$modal.msgError(\"更新失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 刷新详情中的对接记录 */\r\n    refreshDetailDockingList() {\r\n      this.$set(this.detailForm, 'dockingLoading', true);\r\n      request({\r\n        url: `/miniapp/demand/${this.detailForm.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('刷新对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n          console.log('更新后的对接记录:', this.detailForm.dockingList);\r\n          // 强制刷新表格\r\n          this.tableRefreshKey++;\r\n          console.log('服务器数据刷新后，强制刷新表格，新key:', this.tableRefreshKey);\r\n          // 调试数据\r\n          this.debugDockingData();\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 解析表单数据为显示格式 */\r\n    parseFormDataForDisplay(formData) {\r\n      const displayList = [];\r\n\r\n      try {\r\n        // 检查是否是新格式的数据（包含fields数组的对象）\r\n        if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n          // 新格式：遍历所有分类和字段\r\n          formData.forEach(categoryData => {\r\n            if (categoryData.fields && Array.isArray(categoryData.fields)) {\r\n              categoryData.fields.forEach(field => {\r\n                // 跳过静态展示字段\r\n                if (field.type !== 'static' && field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                  displayList.push({\r\n                    label: field.label || field.name,\r\n                    value: field.value,\r\n                    type: field.type || 'input'\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          });\r\n        } else if (typeof formData === 'object' && formData !== null) {\r\n          // 旧格式：直接遍历对象属性\r\n          Object.keys(formData).forEach(key => {\r\n            const value = formData[key];\r\n            if (value !== undefined && value !== null && value !== '') {\r\n              displayList.push({\r\n                label: key,\r\n                value: value,\r\n                type: 'input' // 默认类型\r\n              });\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error('解析表单数据失败:', e);\r\n      }\r\n\r\n      return displayList;\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      return parts[parts.length - 1] || '未知文件';\r\n    },\r\n\r\n    /** 获取联系结果标签类型 */\r\n    getContactResultType(result) {\r\n      const typeMap = {\r\n        '联系成功': 'success',\r\n        '已有合作': 'success',\r\n        '无人接听': 'warning',\r\n        '稍后联系': 'warning',\r\n        '号码错误': 'danger',\r\n        '拒绝沟通': 'danger',\r\n        '不感兴趣': 'info',\r\n        '其他': 'info'\r\n      };\r\n      return typeMap[result] || 'info';\r\n    },\r\n\r\n    /** 调试：检查当前对接记录数据 */\r\n    debugDockingData() {\r\n      console.log('=== 调试对接记录数据 ===');\r\n      console.log('detailForm.dockingList:', this.detailForm.dockingList);\r\n      if (this.detailForm.dockingList && this.detailForm.dockingList.length > 0) {\r\n        this.detailForm.dockingList.forEach((item, index) => {\r\n          console.log(`记录${index + 1}:`, {\r\n            dockingId: item.dockingId,\r\n            userName: item.userName,\r\n            isContacted: item.isContacted,\r\n            isContactedType: typeof item.isContacted\r\n          });\r\n        });\r\n      }\r\n      console.log('tableRefreshKey:', this.tableRefreshKey);\r\n      console.log('========================');\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const demandIds = row ? [row.demandId] : this.ids;\r\n      const confirmText = row\r\n        ? `是否确认删除需求编号为\"${row.demandId}\"的数据项？`\r\n        : `是否确认删除选中的${this.ids.length}条数据项？`;\r\n\r\n      this.$modal.confirm(confirmText).then(function() {\r\n        return delDemand(demandIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/demand/export', {\r\n        ...this.queryParams\r\n      }, `需求数据_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 置顶/取消置顶 */\r\n    handleToggleTop(row) {\r\n      const text = row.isTop === \"1\" ? \"取消置顶\" : \"置顶\";\r\n      const isTop = row.isTop === \"1\" ? \"0\" : \"1\";\r\n      this.$modal.confirm('确认要\"' + text + '\"需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        const updateData = {\r\n          demandId: row.demandId,\r\n          isTop: isTop\r\n        };\r\n        return updateDemand(updateData);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下架需求 */\r\n    handleOffShelf(row) {\r\n      this.$modal.confirm('确认要下架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return offShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"下架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 上架需求 */\r\n    handleOnShelf(row) {\r\n      this.$modal.confirm('确认要上架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return onShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"上架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 需求类型变化事件 */\r\n    onCategoryChange(categoryId) {\r\n      console.log('onCategoryChange - categoryId:', categoryId);\r\n\r\n      // 清空动态表单数据\r\n      this.form.dynamicData = {};\r\n      // 清空分类字段数据\r\n      this.categoryFieldsData = [];\r\n\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      console.log('onCategoryChange - found category:', category);\r\n\r\n      if (category && category.formFields) {\r\n        try {\r\n          const formConfig = JSON.parse(category.formFields);\r\n          console.log('onCategoryChange - formConfig:', formConfig);\r\n\r\n          // 检查是否是新格式的数据（包含fields数组的对象）\r\n          if (Array.isArray(formConfig) && formConfig.length > 0 && formConfig[0].fields) {\r\n            // 新格式：使用分类字段数据\r\n            this.processCategoryFieldsData(formConfig);\r\n            console.log('onCategoryChange - using new format, categoryFieldsData:', this.categoryFieldsData);\r\n          } else {\r\n            // 旧格式：使用传统的动态字段加载\r\n            this.loadDynamicFields(categoryId);\r\n            console.log('onCategoryChange - using old format, dynamicFields:', this.dynamicFields);\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单配置失败:', e);\r\n          this.loadDynamicFields(categoryId);\r\n        }\r\n      } else {\r\n        console.log('onCategoryChange - no category or formFields found');\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n      }\r\n    },\r\n\r\n    /** 加载动态表单字段 */\r\n    loadDynamicFields(categoryId) {\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      if (category) {\r\n        this.selectedCategoryName = category.categoryName;\r\n\r\n        if (category.formFields) {\r\n          try {\r\n            const formConfig = JSON.parse(category.formFields);\r\n            this.dynamicFields = [];\r\n\r\n            // 检查是否是新的模块化结构\r\n            if (Array.isArray(formConfig) && formConfig.length > 0) {\r\n              if (formConfig[0].fields) {\r\n                // 新的模块化结构：提取所有模块中的字段\r\n                formConfig.forEach(module => {\r\n                  if (module.fields && Array.isArray(module.fields)) {\r\n                    module.fields.forEach(field => {\r\n                      // 跳过静态展示字段\r\n                      if (field.type !== 'static' && field.name) {\r\n                        this.dynamicFields.push({\r\n                          ...field,\r\n                          moduleTitle: module.name // 添加模块标题用于分组显示\r\n                        });\r\n                      }\r\n                    });\r\n                  }\r\n                });\r\n              } else {\r\n                // 旧的扁平结构：直接使用\r\n                this.dynamicFields = formConfig;\r\n              }\r\n            }\r\n\r\n            // 初始化动态数据对象和验证规则\r\n            this.dynamicFields.forEach(field => {\r\n              if (field.name) {\r\n                // 确保字段总是有正确的初始值\r\n                if (field.type === 'checkbox') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    Array.isArray(this.form.dynamicData[field.name]) ? this.form.dynamicData[field.name] : []);\r\n                } else if (field.type === 'file') {\r\n                  // 处理文件字段的数据转换\r\n                  const fileData = this.form.dynamicData[field.name];\r\n                  if (typeof fileData === 'string' && fileData.trim() !== '') {\r\n                    // 如果是字符串URL，转换为对象数组格式\r\n                    const fileName = fileData.split('/').pop() || '下载文件';\r\n                    this.$set(this.form.dynamicData, field.name, [{\r\n                      name: fileName,\r\n                      url: fileData\r\n                    }]);\r\n                  } else if (Array.isArray(fileData)) {\r\n                    // 如果已经是数组，保持不变\r\n                    this.$set(this.form.dynamicData, field.name, fileData);\r\n                  } else {\r\n                    // 其他情况设为空数组\r\n                    this.$set(this.form.dynamicData, field.name, []);\r\n                  }\r\n                } else if (field.type === 'number') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else if (field.type === 'date' || field.type === 'time') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : '');\r\n                }\r\n\r\n                // 添加动态字段的验证规则\r\n                if (field.required) {\r\n                  const ruleName = `dynamicData.${field.name}`;\r\n                  this.$set(this.rules, ruleName, [\r\n                    {\r\n                      required: true,\r\n                      message: `${field.label}不能为空`,\r\n                      trigger: field.type === 'checkbox' ? 'change' : 'blur'\r\n                    }\r\n                  ]);\r\n                }\r\n              }\r\n            });\r\n          } catch (e) {\r\n            console.error('解析表单字段配置失败:', e);\r\n            this.dynamicFields = [];\r\n          }\r\n        } else {\r\n          this.dynamicFields = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取字段选项 */\r\n    getFieldOptions(field) {\r\n      if (!field.options) return [];\r\n      return field.options.split(',').map(option => option.trim()).filter(option => option);\r\n    },\r\n\r\n    /** 文件上传成功回调 */\r\n    handleFileSuccess(response, file, fileList, field) {\r\n      console.log('handleFileSuccess - response:', response, 'file:', file, 'field:', field.name);\r\n\r\n      if (response.code === 200) {\r\n        const fileUrl = response.url || response.fileName || response.data;\r\n\r\n        // 对于文件类型字段，value直接存储URL链接，不存储文件名或对象结构\r\n        this.handleFieldInput(field, fileUrl);\r\n\r\n        console.log('handleFileSuccess - 文件上传成功，设置URL:', fileUrl);\r\n        console.log('handleFileSuccess - field.value after update:', field.value);\r\n      } else {\r\n        this.$modal.msgError(response.msg || '文件上传失败');\r\n      }\r\n    },\r\n\r\n    /** 文件删除回调 */\r\n    handleFileRemove(file, fileList, field) {\r\n      // 文件删除时，直接清空value字段\r\n      this.handleFieldInput(field, '');\r\n      console.log('handleFileRemove - 文件已删除，清空字段值');\r\n    },\r\n\r\n    /** 获取多选框的安全值 */\r\n    getCheckboxValue(fieldName) {\r\n      const value = this.form.dynamicData[fieldName];\r\n      return Array.isArray(value) ? value : [];\r\n    },\r\n\r\n    /** 更新多选框的值 */\r\n    updateCheckboxValue(fieldName, value) {\r\n      this.$set(this.form.dynamicData, fieldName, Array.isArray(value) ? value : []);\r\n    },\r\n\r\n    /** 获取文件列表（用于el-upload组件） */\r\n    getFileList(field) {\r\n      const files = field.value;\r\n      console.log('getFileList - field:', field.name, 'value:', files);\r\n\r\n      // 如果是字符串URL且不为空，转换为文件列表格式显示在upload组件中\r\n      if (typeof files === 'string' && files.trim() !== '') {\r\n        return [{\r\n          name: this.getFileNameFromUrl(files),\r\n          url: files,\r\n          uid: `${field.name}-0`,\r\n          status: 'success'\r\n        }];\r\n      }\r\n\r\n      // 如果是数组格式（兼容旧数据）\r\n      if (Array.isArray(files)) {\r\n        return files.map((file, index) => ({\r\n          name: file.name || this.getFileNameFromUrl(file.url || file),\r\n          url: file.url || file,\r\n          uid: `${field.name}-${index}`,\r\n          status: 'success'\r\n        }));\r\n      }\r\n\r\n      // 其他情况返回空数组\r\n      console.log('getFileList - 无有效文件数据，返回空数组');\r\n      return [];\r\n    },\r\n\r\n    /** 获取已上传的文件列表（用于显示） */\r\n    getUploadedFiles(field) {\r\n      const files = field.value;\r\n      return Array.isArray(files) ? files : [];\r\n    },\r\n\r\n    /** 文件预览 */\r\n    handleFilePreview(file) {\r\n      if (file.url) {\r\n        window.open(file.url, '_blank');\r\n      }\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(url, fileName) {\r\n      // 创建一个临时的a标签来触发下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 删除已上传的文件 */\r\n    removeUploadedFile(field, index) {\r\n      if (field.value && Array.isArray(field.value)) {\r\n        const newValue = [...field.value];\r\n        newValue.splice(index, 1);\r\n        this.handleFieldInput(field, newValue);\r\n      }\r\n    },\r\n\r\n    /** 删除文件URL */\r\n    removeFileUrl(field) {\r\n      this.handleFieldInput(field, '');\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      const fileName = parts[parts.length - 1];\r\n      // 如果文件名包含时间戳等，尝试提取原始文件名\r\n      const match = fileName.match(/.*_\\d+A\\d+\\.(.*)/);\r\n      if (match) {\r\n        return `文件.${match[1]}`;\r\n      }\r\n      return fileName || '未知文件';\r\n    },\r\n\r\n    /** 处理字段输入 */\r\n    handleFieldInput(field, value) {\r\n      // 更新字段的value\r\n      field.value = value;\r\n      // 同步到表单数据\r\n      this.$set(this.form.dynamicData, field.name, value);\r\n      console.log('handleFieldInput - field:', field.name, 'value:', value);\r\n    },\r\n\r\n    /** 更新字段值到表单数据 */\r\n    updateFieldValue(field) {\r\n      this.$set(this.form.dynamicData, field.name, field.value);\r\n    },\r\n\r\n    /** 获取分类名称 */\r\n    getCategoryName() {\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        return this.categoryFieldsData[0].name || '专属字段';\r\n      }\r\n      return this.selectedCategoryName || '专属字段';\r\n    },\r\n\r\n    /** 处理分类字段数据 */\r\n    processCategoryFieldsData(data) {\r\n      if (typeof data === 'string') {\r\n        try {\r\n          this.categoryFieldsData = JSON.parse(data);\r\n        } catch (e) {\r\n          console.error('解析分类字段数据失败:', e);\r\n          this.categoryFieldsData = [];\r\n        }\r\n      } else if (Array.isArray(data)) {\r\n        this.categoryFieldsData = data;\r\n      } else {\r\n        this.categoryFieldsData = [];\r\n      }\r\n\r\n      // 初始化字段值到表单数据\r\n      this.categoryFieldsData.forEach(categoryData => {\r\n        if (categoryData.fields) {\r\n          categoryData.fields.forEach(field => {\r\n            // 确保字段有初始值\r\n            if (field.value === undefined || field.value === null) {\r\n              if (field.type === 'file') {\r\n                field.value = [];\r\n              } else if (field.type === 'checkbox') {\r\n                field.value = [];\r\n              } else {\r\n                field.value = '';\r\n              }\r\n            }\r\n\r\n            // 从表单数据中恢复字段值（如果存在）\r\n            if (this.form.dynamicData && this.form.dynamicData[field.name] !== undefined) {\r\n              field.value = this.form.dynamicData[field.name];\r\n            } else {\r\n              // 设置到表单数据\r\n              this.$set(this.form.dynamicData, field.name, field.value);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 测试新的数据格式 */\r\n    testNewDataFormat() {\r\n      // 使用您提供的实际JSON数据格式进行测试\r\n      const testData = [\r\n        {\r\n          \"name\": \"基础信息\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"企业全称\",\r\n              \"name\": \"field_652408\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"测试企业有限公司\"\r\n            },\r\n            {\r\n              \"label\": \"行业标签\",\r\n              \"name\": \"field_720944\",\r\n              \"type\": \"select\",\r\n              \"required\": true,\r\n              \"options\": \"新能源,硬科技\",\r\n              \"placeholder\": \"请选择\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"新能源\"\r\n            },\r\n            {\r\n              \"label\": \"联系人\",\r\n              \"name\": \"contact_name\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"张三\"\r\n            },\r\n            {\r\n              \"label\": \"电话\",\r\n              \"name\": \"phone\",\r\n              \"type\": \"tel\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"13800138000\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"\r\n        },\r\n        {\r\n          \"name\": \"其他材料补充\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"上传附件\",\r\n              \"name\": \"field_989222\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"未选择任何文件\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"http://************:8080/profile/upload/2025/07/23/xhuFwa0qulPS03911c35329f695848fb659a24f6f159_20250723183220A001.png\"\r\n            },\r\n            {\r\n              \"label\": \"邮件提交至\",\r\n              \"name\": \"field_227969\",\r\n              \"type\": \"static\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\r\n              \"value\": \"\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"\r\n        }\r\n      ];\r\n\r\n      // 当点击修改按钮时，可以调用这个方法来设置测试数据\r\n      // this.processCategoryFieldsData(testData);\r\n    },\r\n\r\n\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n/* 详情弹窗样式 - 参考简洁布局 */\r\n.detail-content {\r\n  padding: 0;\r\n  background-color: #f5f5f5;\r\n  min-height: 400px;\r\n}\r\n\r\n.info-section {\r\n  background-color: white;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  background-color: #f8f9fa;\r\n  padding: 12px 20px;\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  color: #666;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  min-width: 100px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.info-value {\r\n  color: #333;\r\n  font-size: 14px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.description-text {\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.docking-section {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.docking-list {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.docking-item {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.docking-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-main {\r\n  flex: 1;\r\n}\r\n\r\n.item-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  gap: 10px;\r\n}\r\n\r\n.user-name {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.user-phone {\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.item-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item i {\r\n  margin-right: 4px;\r\n  color: #999;\r\n}\r\n\r\n.item-notes {\r\n  color: #666;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-notes i {\r\n  margin-right: 4px;\r\n  margin-top: 2px;\r\n  color: #999;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.item-actions {\r\n  margin-left: 15px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.dynamic-fields-section {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border: 1px solid #e6e6e6;\r\n}\r\n\r\n.module-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.module-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 分类组样式 */\r\n.category-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.category-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.category-description {\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 动态字段垂直布局样式 */\r\n.dynamic-field-item {\r\n  margin-bottom: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item:last-child {\r\n  margin-bottom: 20px; /* 保持底部间距，避免与下方元素重合 */\r\n}\r\n\r\n/* 字段标签样式 */\r\n.dynamic-field-item .field-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 必填字段标识 */\r\n.dynamic-field-item .required-mark {\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 字段内容区域 */\r\n.dynamic-field-item .field-content {\r\n  width: 100%;\r\n}\r\n\r\n/* 表单控件样式 */\r\n.dynamic-field-item .field-content .el-input,\r\n.dynamic-field-item .field-content .el-textarea,\r\n.dynamic-field-item .field-content .el-select,\r\n.dynamic-field-item .field-content .el-input-number,\r\n.dynamic-field-item .field-content .el-date-editor,\r\n.dynamic-field-item .field-content .el-time-picker {\r\n  width: 100%;\r\n}\r\n\r\n/* 单选框和多选框布局 */\r\n.dynamic-field-item .field-content .el-radio-group,\r\n.dynamic-field-item .field-content .el-checkbox-group {\r\n  width: 100%;\r\n  line-height: 1.8;\r\n}\r\n\r\n.dynamic-field-item .field-content .el-radio,\r\n.dynamic-field-item .field-content .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 文件上传组件 */\r\n.dynamic-field-item .field-content .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.el-divider {\r\n  margin: 10px 0 20px 0;\r\n}\r\n\r\n/* 响应式布局优化 */\r\n@media (max-width: 768px) {\r\n  .dynamic-field-item .el-form-item__label {\r\n    width: 100px !important;\r\n    text-align: left;\r\n  }\r\n\r\n  .dynamic-field-item .el-radio,\r\n  .dynamic-field-item .el-checkbox {\r\n    display: block;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 上传组件样式优化 */\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.dynamic-field-item .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item .el-upload-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 文件上传相关样式 */\r\n.uploaded-files-list {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 4px;\r\n}\r\n\r\n.uploaded-files-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.uploaded-file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.uploaded-file-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.uploaded-file-item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-link:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.remove-file-btn {\r\n  margin-left: 10px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.remove-file-btn:hover {\r\n  color: #f78989;\r\n}\r\n\r\n/* 静态内容样式 */\r\n.static-content {\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 已存在文件显示样式 */\r\n.existing-file {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-display {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 4px;\r\n  gap: 8px;\r\n}\r\n\r\n.file-display .el-icon-document {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-display .file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-display .file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.file-display .remove-file-btn {\r\n  color: #f56c6c;\r\n  padding: 0;\r\n}\r\n\r\n/* 动态字段整体布局优化 */\r\n.dynamic-fields-section .el-row {\r\n  margin-left: -10px;\r\n  margin-right: -10px;\r\n}\r\n\r\n.dynamic-fields-section .el-col {\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n\r\n/* 优化表单验证错误提示的显示 */\r\n.dynamic-field-item .el-form-item__error {\r\n  position: static;\r\n  margin-top: 2px;\r\n  padding-top: 2px;\r\n}\r\n\r\n/* 对接情况显示样式 */\r\n.docking-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.contact-stats {\r\n  display: flex;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-content h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-docking {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n/* 对接详情表格样式 */\r\n.expand-content .el-table {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.expand-content .el-table th {\r\n  background-color: #fafafa;\r\n}\r\n</style>\r\n"]}]}