<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniDemandDockingMapper">
    
    <resultMap type="MiniDemandDocking" id="MiniDemandDockingResult">
        <result property="dockingId"    column="docking_id"    />
        <result property="demandId"    column="demand_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="userCompany"    column="user_company"    />
        <result property="userPosition"    column="user_position"    />
        <result property="dockingTime"    column="docking_time"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactSource"    column="contact_source"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="demandTitle"    column="demand_title"    />
        <result property="isContacted"    column="is_contacted"    />
        <result property="contactResult"    column="contact_result"    />
        <result property="contactNotes"    column="contact_notes"    />
        <result property="contactTime"    column="contact_time"    />
        <result property="qrCodeUrl"    column="qr_code_url"    />
    </resultMap>

    <resultMap type="MiniDemandDocking" id="MiniDemandDockingDetailResult" extends="MiniDemandDockingResult">
        <association property="demandInfo" javaType="com.ruoyi.miniapp.domain.MiniDemandDocking$DemandInfo">
            <result property="demandId" column="demand_demand_id"/>
            <result property="demandTitle" column="demand_title"/>
            <result property="demandDesc" column="demand_desc"/>
            <result property="categoryName" column="category_name"/>
            <result property="categoryShortName" column="category_short_name"/>
            <result property="categoryCode" column="category_code"/>
            <result property="demandStatus" column="demand_status"/>
            <result property="viewCount" column="view_count"/>
            <result property="formData" column="form_data"/>
        </association>
        <association property="userInfo" javaType="com.ruoyi.miniapp.domain.MiniDemandDocking$UserInfo">
            <result property="userId" column="user_user_id"/>
            <result property="userName" column="user_user_name"/>
            <result property="nickName" column="user_nick_name"/>
            <result property="avatar" column="user_avatar"/>
            <result property="realName" column="user_real_name"/>
            <result property="phonenumber" column="user_phonenumber"/>
            <result property="currentCompany" column="user_current_company"/>
            <result property="positionTitle" column="user_position_title"/>
        </association>
    </resultMap>

    <sql id="selectMiniDemandDockingVo">
        select docking_id, demand_id, user_id, user_name, user_phone, user_company, user_position,
               docking_time, contact_name, contact_phone, contact_source, status,
               is_contacted, contact_result, contact_notes, contact_time, qr_code_url,
               create_by, create_time, update_by, update_time, remark
        from mini_demand_docking
    </sql>

    <select id="selectMiniDemandDockingList" parameterType="MiniDemandDocking" resultMap="MiniDemandDockingResult">
        select dd.docking_id, dd.demand_id, dd.user_id, dd.user_name, dd.user_phone, dd.user_company, dd.user_position,
               dd.docking_time, dd.contact_name, dd.contact_phone, dd.contact_source, dd.status,
               dd.is_contacted, dd.contact_result, dd.contact_notes, dd.contact_time,
               dd.create_by, dd.create_time, dd.update_by, dd.update_time, dd.remark,
               d.demand_title
        from mini_demand_docking dd
        left join mini_demand d on dd.demand_id = d.demand_id
        <where>
            <if test="demandId != null "> and dd.demand_id = #{demandId}</if>
            <if test="userId != null "> and dd.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and dd.user_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and dd.user_phone = #{userPhone}</if>
            <if test="contactSource != null  and contactSource != ''"> and dd.contact_source = #{contactSource}</if>
            <if test="status != null  and status != ''"> and dd.status = #{status}</if>
            <if test="demandTitle != null  and demandTitle != ''"> and d.demand_title like concat('%', #{demandTitle}, '%')</if>
            <if test="params.beginDockingTime != null and params.beginDockingTime != ''"><!-- 开始时间检索 -->
                and date_format(dd.docking_time,'%y%m%d') &gt;= date_format(#{params.beginDockingTime},'%y%m%d')
            </if>
            <if test="params.endDockingTime != null and params.endDockingTime != ''"><!-- 结束时间检索 -->
                and date_format(dd.docking_time,'%y%m%d') &lt;= date_format(#{params.endDockingTime},'%y%m%d')
            </if>
        </where>
        order by dd.docking_time desc
    </select>
    
    <select id="selectMiniDemandDockingByDockingId" parameterType="Long" resultMap="MiniDemandDockingResult">
        <include refid="selectMiniDemandDockingVo"/>
        where docking_id = #{dockingId}
    </select>

    <select id="selectDockingRecord" resultMap="MiniDemandDockingResult">
        <include refid="selectMiniDemandDockingVo"/>
        where demand_id = #{demandId} and user_id = #{userId}
    </select>

    <select id="selectDockingListByDemandId" resultMap="MiniDemandDockingResult">
        <include refid="selectMiniDemandDockingVo"/>
        where demand_id = #{demandId} and status = '0'
        order by docking_time desc
    </select>

    <select id="selectMyDockingList" resultMap="MiniDemandDockingDetailResult">
        select 
            dd.docking_id, dd.demand_id, dd.user_id, dd.user_name, dd.user_phone, 
            dd.user_company, dd.user_position, dd.docking_time, dd.contact_name, 
            dd.contact_phone, dd.contact_source, dd.status, dd.create_by, 
            dd.create_time, dd.update_by, dd.update_time, dd.remark,
            d.demand_id as demand_demand_id,
            d.demand_title,
            d.demand_desc,
            d.demand_status,
            d.form_data,
            dc.category_name,
            dc.category_short_name,
            dc.category_code,
            d.view_count
        from mini_demand_docking dd
        left join mini_demand d on dd.demand_id = d.demand_id
        left join mini_demand_category dc on d.category_id = dc.category_id
        where dd.user_id = #{userId} and dd.status = '0'
        order by dd.docking_time desc
    </select>

    <select id="countDockingByDemandId" resultType="int">
        select count(*) from mini_demand_docking 
        where demand_id = #{demandId} and status = '0'
    </select>

    <select id="countDockingByUserId" resultType="int">
        select count(*) from mini_demand_docking 
        where user_id = #{userId} and status = '0'
    </select>

    <select id="batchSelectDockingStatus" resultMap="MiniDemandDockingResult">
        <include refid="selectMiniDemandDockingVo"/>
        where user_id = #{userId} 
        and demand_id in
        <foreach item="demandId" collection="demandIds" open="(" separator="," close=")">
            #{demandId}
        </foreach>
    </select>

    <select id="selectDockingDetailById" resultMap="MiniDemandDockingDetailResult">
        select 
            dd.docking_id, dd.demand_id, dd.user_id, dd.user_name, dd.user_phone, 
            dd.user_company, dd.user_position, dd.docking_time, dd.contact_name, 
            dd.contact_phone, dd.contact_source, dd.status, dd.create_by, 
            dd.create_time, dd.update_by, dd.update_time, dd.remark,
            d.demand_id as demand_demand_id,
            d.demand_title,
            d.demand_desc,
            d.demand_status,
            dc.category_name,
            u.user_id as user_user_id,
            u.user_name as user_user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            u.real_name as user_real_name,
            u.phonenumber as user_phonenumber,
            u.current_company as user_current_company,
            u.position_title as user_position_title
        from mini_demand_docking dd
        left join mini_demand d on dd.demand_id = d.demand_id
        left join mini_demand_category dc on d.category_id = dc.category_id
        left join sys_user u on dd.user_id = u.user_id
        where dd.docking_id = #{dockingId}
    </select>

    <select id="selectDockingDetailListByDemandId" resultMap="MiniDemandDockingDetailResult">
        select 
            dd.docking_id, dd.demand_id, dd.user_id, dd.user_name, dd.user_phone, 
            dd.user_company, dd.user_position, dd.docking_time, dd.contact_name, 
            dd.contact_phone, dd.contact_source, dd.status, dd.create_by, dd.is_contacted,
            dd.create_time, dd.update_by, dd.update_time, dd.remark,
            u.user_id as user_user_id,
            u.user_name as user_user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            u.real_name as user_real_name,
            u.phonenumber as user_phonenumber,
            u.current_company as user_current_company,
            u.position_title as user_position_title
        from mini_demand_docking dd
        left join sys_user u on dd.user_id = u.user_id
        where dd.demand_id = #{demandId} and dd.status = '0'
        order by dd.docking_time desc
    </select>
        
    <insert id="insertMiniDemandDocking" parameterType="MiniDemandDocking" useGeneratedKeys="true" keyProperty="dockingId">
        insert into mini_demand_docking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="demandId != null">demand_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="userCompany != null">user_company,</if>
            <if test="userPosition != null">user_position,</if>
            <if test="dockingTime != null">docking_time,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactSource != null">contact_source,</if>
            <if test="qrCodeUrl != null">qr_code_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="demandId != null">#{demandId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="userCompany != null">#{userCompany},</if>
            <if test="userPosition != null">#{userPosition},</if>
            <if test="dockingTime != null">#{dockingTime},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactSource != null">#{contactSource},</if>
            <if test="qrCodeUrl != null">#{qrCodeUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniDemandDocking" parameterType="MiniDemandDocking">
        update mini_demand_docking
        <trim prefix="SET" suffixOverrides=",">
            <if test="demandId != null">demand_id = #{demandId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userPhone != null">user_phone = #{userPhone},</if>
            <if test="userCompany != null">user_company = #{userCompany},</if>
            <if test="userPosition != null">user_position = #{userPosition},</if>
            <if test="dockingTime != null">docking_time = #{dockingTime},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactSource != null">contact_source = #{contactSource},</if>
            <if test="qrCodeUrl != null">qr_code_url = #{qrCodeUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isContacted != null">is_contacted = #{isContacted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where docking_id = #{dockingId}
    </update>



    <delete id="deleteMiniDemandDockingByDockingId" parameterType="Long">
        delete from mini_demand_docking where docking_id = #{dockingId}
    </delete>

    <delete id="deleteMiniDemandDockingByDockingIds" parameterType="String">
        delete from mini_demand_docking where docking_id in 
        <foreach item="dockingId" collection="array" open="(" separator="," close=")">
            #{dockingId}
        </foreach>
    </delete>
</mapper>
