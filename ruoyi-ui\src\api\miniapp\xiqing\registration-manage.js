import request from '@/utils/request'

// 查询西青金种子路演报名管理列表
export function listRegistrationManage(query) {
  return request({
    url: '/miniapp/xiqing/registration-manage/list',
    method: 'get',
    params: query
  })
}

// 查询西青金种子路演报名管理详细
export function getRegistrationManage(registrationId) {
  return request({
    url: '/miniapp/xiqing/registration-manage/' + registrationId,
    method: 'get'
  })
}

// 删除西青金种子路演报名管理
export function delRegistrationManage(registrationIds) {
  return request({
    url: '/miniapp/xiqing/registration-manage/' + registrationIds,
    method: 'delete'
  })
}

// 导出西青金种子路演报名管理
export function exportRegistrationManage(query) {
  return request({
    url: '/miniapp/xiqing/registration-manage/export',
    method: 'get',
    params: query
  })
}

// 审核路演报名
export function auditRegistrationManage(data) {
  return request({
    url: '/miniapp/xiqing/registration-manage/audit',
    method: 'put',
    data: data
  })
}
