<template>
  <div class="app-container">
    <div class="contact-info-container">
      <div class="contact-header">
        <h3>报名咨询联系方式</h3>
        <el-button
          type="primary"
          icon="el-icon-edit"
          @click="handleEdit"
          v-hasPermi="['miniapp:xiqing:consultation:edit']"
        >编辑</el-button>
      </div>

      <div class="contact-content" v-loading="loading">
        <div class="contact-item">
          <label>联系人：</label>
          <span>{{ contactInfo.contactName || '暂未设置' }}</span>
        </div>
        <div class="contact-item">
          <label>联系方式：</label>
          <span>{{ contactInfo.contactMethod || '暂未设置' }}</span>
        </div>
        <div class="contact-item" v-if="contactInfo.remark">
          <label>备注：</label>
          <span>{{ contactInfo.remark }}</span>
        </div>
      </div>
    </div>

    <!-- 编辑联系信息对话框 -->
    <el-dialog title="编辑联系信息" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="form.contactName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactMethod">
          <el-input v-model="form.contactMethod" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getContactInfo, updateConsultation } from "@/api/miniapp/xiqing/consultation";

export default {
  name: "XiqingConsultation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
      // 联系信息
      contactInfo: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        contactName: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        contactMethod: [
          { required: true, message: "联系方式不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getContactInfo();
  },
  methods: {
    /** 获取联系信息 */
    getContactInfo() {
      this.loading = true;
      getContactInfo().then(response => {
        this.contactInfo = response.data || {};
        this.loading = false;
      }).catch(() => {
        this.contactInfo = {};
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        consultationId: null,
        contactName: null,
        contactMethod: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 编辑按钮操作 */
    handleEdit() {
      this.reset();
      this.form = {
        consultationId: this.contactInfo.consultationId || null,
        contactName: this.contactInfo.contactName || '',
        contactMethod: this.contactInfo.contactMethod || '',
        status: this.contactInfo.status || "0",
        remark: this.contactInfo.remark || ''
      };
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          updateConsultation(this.form).then(response => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getContactInfo();
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.contact-info-container {
  max-width: 600px;
  margin: 20px auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.contact-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.contact-content {
  padding: 24px;
  min-height: 120px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  font-size: 14px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item label {
  min-width: 80px;
  color: #606266;
  font-weight: 500;
  margin-right: 12px;
}

.contact-item span {
  color: #303133;
  flex: 1;
  word-break: break-all;
}
</style>