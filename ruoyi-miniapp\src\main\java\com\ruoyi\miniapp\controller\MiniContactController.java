package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniContact;
import com.ruoyi.miniapp.service.IMiniContactService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 联系人管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */                                                                                                                                                                                                                                                                                                                                                                                                                                            
@Api(tags = "联系人管理")
@RestController
@RequestMapping("/miniapp/contact")
public class MiniContactController extends BaseController
{
    @Autowired
    private IMiniContactService miniContactService;

    /**
     * 查询联系人管理列表
     */
    @ApiOperation("查询联系人管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniContact miniContact)
    {
        startPage();
        List<MiniContact> list = miniContactService.selectMiniContactList(miniContact);
        return getDataTable(list);
    }

    /**
     * 导出联系人管理列表
     */
    @ApiOperation("导出联系人管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:export')")
    @Log(title = "联系人管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniContact miniContact)
    {
        List<MiniContact> list = miniContactService.selectMiniContactList(miniContact);
        ExcelUtil<MiniContact> util = new ExcelUtil<MiniContact>(MiniContact.class);
        util.exportExcel(response, list, "联系人管理数据");
    }

    /**
     * 获取联系人管理详细信息
     */
    @ApiOperation("获取联系人管理详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("联系人ID") @RequestBody Long contactId)
    {
        return AjaxResult.success(miniContactService.selectMiniContactByContactId(contactId));
    }

    /**
     * 新增联系人管理
     */
    @ApiOperation("新增联系人管理")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:add')")
    @Log(title = "联系人管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("联系人信息") @RequestBody MiniContact miniContact)
    {
        return toAjax(miniContactService.insertMiniContact(miniContact));
    }

    /**
     * 修改联系人管理
     */
    @ApiOperation("修改联系人管理")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:edit')")
    @Log(title = "联系人管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("联系人信息") @RequestBody MiniContact miniContact)
    {
        return toAjax(miniContactService.updateMiniContact(miniContact));
    }

    /**
     * 删除联系人管理
     */
    @ApiOperation("删除联系人管理")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:remove')")
    @Log(title = "联系人管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("联系人ID数组") @RequestBody Long[] contactIds)
    {
        return toAjax(miniContactService.deleteMiniContactByContactIds(contactIds));
    }

    /**
     * 更新联系人排序
     */
    @ApiOperation("更新联系人排序")
    @PreAuthorize("@ss.hasPermi('miniapp:contact:edit')")
    @Log(title = "联系人管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateSort")
    public AjaxResult updateSort(@ApiParam("联系人信息") @RequestBody MiniContact miniContact)
    {
        return toAjax(miniContactService.updateMiniContactSort(miniContact));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的联系人列表
     */
    @ApiOperation("获取启用的联系人列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniContact> list = miniContactService.selectEnabledMiniContactList();
        return AjaxResult.success(list);
    }
}
