package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.XiqingRoadshowActivity;
import com.ruoyi.miniapp.service.IXiqingRoadshowActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 西青金种子路演活动配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "西青金种子-路演活动配置")
@RestController
@RequestMapping("/miniapp/xiqing/activity-config")
public class XiqingRoadshowActivityController extends BaseController
{
    @Autowired
    private IXiqingRoadshowActivityService xiqingRoadshowActivityService;

    /**
     * 查询西青金种子路演活动配置列表
     */
    @ApiOperation("查询路演活动配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        startPage();
        List<XiqingRoadshowActivity> list = xiqingRoadshowActivityService.selectXiqingRoadshowActivityList(xiqingRoadshowActivity);
        return getDataTable(list);
    }

    /**
     * 导出西青金种子路演活动配置列表
     */
    @ApiOperation("导出路演活动配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:export')")
    @Log(title = "西青金种子路演活动配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        List<XiqingRoadshowActivity> list = xiqingRoadshowActivityService.selectXiqingRoadshowActivityList(xiqingRoadshowActivity);
        ExcelUtil<XiqingRoadshowActivity> util = new ExcelUtil<XiqingRoadshowActivity>(XiqingRoadshowActivity.class);
        util.exportExcel(response, list, "西青金种子路演活动配置数据");
    }

    /**
     * 获取西青金种子路演活动配置详细信息
     */
    @ApiOperation("获取路演活动配置详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:query')")
    @GetMapping(value = "/{activityId}")
    public AjaxResult getInfo(@ApiParam("活动ID") @PathVariable("activityId") Long activityId)
    {
        return AjaxResult.success(xiqingRoadshowActivityService.selectXiqingRoadshowActivityByActivityId(activityId));
    }

    /**
     * 新增西青金种子路演活动配置
     */
    @ApiOperation("新增路演活动配置")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:add')")
    @Log(title = "西青金种子路演活动配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("活动配置信息") @RequestBody XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        return toAjax(xiqingRoadshowActivityService.insertXiqingRoadshowActivity(xiqingRoadshowActivity));
    }

    /**
     * 修改西青金种子路演活动配置
     */
    @ApiOperation("修改路演活动配置")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:edit')")
    @Log(title = "西青金种子路演活动配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("活动配置信息") @RequestBody XiqingRoadshowActivity xiqingRoadshowActivity)
    {
        return toAjax(xiqingRoadshowActivityService.updateXiqingRoadshowActivity(xiqingRoadshowActivity));
    }

    /**
     * 删除西青金种子路演活动配置
     */
    @ApiOperation("删除路演活动配置")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:remove')")
    @Log(title = "西青金种子路演活动配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{activityIds}")
    public AjaxResult remove(@ApiParam("活动ID数组") @PathVariable Long[] activityIds)
    {
        return toAjax(xiqingRoadshowActivityService.deleteXiqingRoadshowActivityByActivityIds(activityIds));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的路演活动列表（小程序端）
     */
    @ApiOperation("获取启用的路演活动列表")
    @GetMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<XiqingRoadshowActivity> list = xiqingRoadshowActivityService.selectEnabledXiqingRoadshowActivityList();
        return AjaxResult.success(list);
    }

    /**
     * 获取路演活动详情（小程序端）
     */
    @ApiOperation("获取路演活动详情")
    @GetMapping("/app/getDetail/{activityId}")
    public AjaxResult getDetail(@ApiParam("活动ID") @PathVariable("activityId") Long activityId)
    {
        XiqingRoadshowActivity activity = xiqingRoadshowActivityService.selectXiqingRoadshowActivityByActivityId(activityId);
        if (activity == null || !"0".equals(activity.getStatus())) {
            return AjaxResult.error("活动不存在或已停用");
        }
        return AjaxResult.success(activity);
    }

    /**
     * 获取默认路演活动详情（小程序端）
     */
    @ApiOperation("获取默认路演活动详情")
    @GetMapping("/app/getDefaultActivity")
    public AjaxResult getDefaultActivity()
    {
        // 获取ID为1的默认路演活动
        XiqingRoadshowActivity activity = xiqingRoadshowActivityService.selectXiqingRoadshowActivityByActivityId(1L);
        if (activity == null || !"0".equals(activity.getStatus())) {
            return AjaxResult.error("活动不存在或已停用");
        }
        return AjaxResult.success(activity);
    }
}
