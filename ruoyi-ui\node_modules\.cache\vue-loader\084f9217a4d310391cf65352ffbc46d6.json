{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=template&id=733e5256&scoped=true", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753755689596}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752653998535}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}