{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=template&id=733e5256&scoped=true", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753755064715}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752653998535}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}