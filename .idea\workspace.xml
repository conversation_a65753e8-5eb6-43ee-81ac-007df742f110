<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="创赛路演：园区管理补充&#10;新增：西青金种子路演专区&#10;     活动管理，活动配置，报名咨询，报名管理。">
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_22_16_30_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_22_16_30__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_23_10_58_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_23_10_58__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_25_17_35_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_25_17_35__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_28_9_05_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_28_9_05__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/修改总结.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_7_15_43__Changes_.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/HaitangTopImageController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/HaitangTopImageMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IHaitangTopImageService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/HaitangTopImageServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/HaitangTopImageMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/HaitangTopImageMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/新闻中心模块使用说明.md" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/新闻中心模块使用说明.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../maven/maven/apache-maven-3.9.6" />
        <option name="localRepository" value="D:\maven\maven\mvn-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\maven\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zInxgJ5QRhFOLKqgYyg52o7LSc" />
  <component name="ProjectViewState">
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-admin [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/develop/cursorProject/national_debt&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Problems&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2024.2.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\项目记录（吴龙龙）\tjuhaitang_miniapp\ruoyi-ui\src\assets\icons\svg" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="RuoYi-Vue" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="" />
      <created>1751427704689</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751427704689</updated>
      <workItem from="1751427708659" duration="47875000" />
      <workItem from="1751855410839" duration="795000" />
      <workItem from="1751856240516" duration="21000" />
      <workItem from="1751856270005" duration="37696000" />
      <workItem from="1751960768436" duration="415000" />
      <workItem from="1751961217500" duration="63000" />
      <workItem from="1751961297784" duration="142000" />
      <workItem from="1751961476241" duration="77000" />
      <workItem from="1751961563445" duration="581000" />
      <workItem from="1751962169130" duration="8266000" />
      <workItem from="1753084247846" duration="2495000" />
      <workItem from="1753087612818" duration="27184000" />
      <workItem from="1753669465181" duration="14661000" />
    </task>
    <task id="LOCAL-00001" summary="项目构建">
      <option name="closed" value="true" />
      <created>1751857168179</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751857168179</updated>
    </task>
    <task id="LOCAL-00002" summary="需求广场over">
      <option name="closed" value="true" />
      <created>1751874137094</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751874137094</updated>
    </task>
    <task id="LOCAL-00003" summary="创赛路演：注释回来了">
      <option name="closed" value="true" />
      <created>1753092047575</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753092047575</updated>
    </task>
    <task id="LOCAL-00004" summary="创赛路演：项目报名调整">
      <option name="closed" value="true" />
      <created>1753149037099</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753149037099</updated>
    </task>
    <task id="LOCAL-00005" summary="创赛路演：项目报名提交">
      <option name="closed" value="true" />
      <created>1753152595455</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753152595455</updated>
    </task>
    <task id="LOCAL-00006" summary="创赛路演：删除了海棠杯轮播图，新增项目辅导报名提交，项目报名赞助商图片提交/获取。">
      <option name="closed" value="true" />
      <created>1753168858949</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753168858949</updated>
    </task>
    <task id="LOCAL-00007" summary="创赛路演：园区管理">
      <option name="closed" value="true" />
      <created>1753440103491</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753440103491</updated>
    </task>
    <task id="LOCAL-00008" summary="创赛路演：园区管理补充&#10;新增：西青金种子路演专区&#10;     活动管理，活动配置，报名咨询，报名管理。">
      <option name="closed" value="true" />
      <created>1753694158703</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753694158703</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="项目构建" />
    <MESSAGE value="需求广场over" />
    <MESSAGE value="创赛路演：注释回来了" />
    <MESSAGE value="创赛路演：项目报名调整" />
    <MESSAGE value="创赛路演：项目报名提交" />
    <MESSAGE value="创赛路演：删除了海棠杯轮播图，新增项目辅导报名提交，项目报名赞助商图片提交/获取。" />
    <MESSAGE value="创赛路演：园区管理" />
    <MESSAGE value="创赛路演：园区管理补充&#10;新增：西青金种子路演专区&#10;     活动管理，活动配置，报名咨询，报名管理。" />
    <option name="LAST_COMMIT_MESSAGE" value="创赛路演：园区管理补充&#10;新增：西青金种子路演专区&#10;     活动管理，活动配置，报名咨询，报名管理。" />
  </component>
</project>