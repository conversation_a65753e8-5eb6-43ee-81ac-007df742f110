{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=template&id=71514f24&scoped=true", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753759379321}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752653998535}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}