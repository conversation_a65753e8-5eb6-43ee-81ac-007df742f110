{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\contact\\index.vue?vue&type=style&index=0&id=65b57d7e&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\contact\\index.vue", "mtime": 1753759379332}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752653987088}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752653998445}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752653991020}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752653993569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLm5vLXFyY29kZSB7DQogIGNvbG9yOiAjYzBjNGNjOw0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5mb3JtLXRpcCB7DQogIG1hcmdpbi10b3A6IDhweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLmZvcm0tdGlwIHAgew0KICBtYXJnaW46IDJweCAwOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+XA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/content/contact", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"联系人\" prop=\"contactName\">\r\n        <el-input\r\n          v-model=\"queryParams.contactName\"\r\n          placeholder=\"请输入联系人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"标识\" prop=\"contactCode\">\r\n        <el-input\r\n          v-model=\"queryParams.contactCode\"\r\n          placeholder=\"请输入联系人标识\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"电话\" prop=\"contactPhone\">\r\n        <el-input\r\n          v-model=\"queryParams.contactPhone\"\r\n          placeholder=\"请输入联系电话\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:contact:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:contact:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:contact:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:contact:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"contactList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"contactId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"联系人ID\" align=\"center\" prop=\"contactId\" width=\"100\" />\r\n      <el-table-column label=\"联系人姓名\" align=\"center\" prop=\"contactName\" width=\"120\" />\r\n      <el-table-column label=\"联系人标识\" align=\"center\" prop=\"contactCode\" width=\"120\" />\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" width=\"150\" />\r\n      <el-table-column label=\"联系二维码\" align=\"center\" prop=\"qrCodeUrl\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.qrCodeUrl\" :width=\"60\" :height=\"60\" v-if=\"scope.row.qrCodeUrl\"/>\r\n          <span v-else class=\"no-qrcode\">暂无二维码</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number\r\n            v-model=\"scope.row.sortOrder\"\r\n            :min=\"0\"\r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 80px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"160\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:contact:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:contact:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改联系人管理对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"联系人姓名\" prop=\"contactName\">\r\n          <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系人标识\" prop=\"contactCode\">\r\n          <el-input v-model=\"form.contactCode\" placeholder=\"请输入联系人唯一标识，如：SERVICE_01\" />\r\n          <div class=\"form-tip\">\r\n            <p>• 用于标识联系人的唯一代码</p>\r\n            <p>• 建议格式：SERVICE_01、TECH_01、BUSINESS_01</p>\r\n            <p>• 一旦设置不建议修改</p>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n          <el-input v-model=\"form.contactPhone\" placeholder=\"请输入联系电话\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系二维码\" prop=\"qrCodeUrl\">\r\n          <ImageUpload\r\n            v-model=\"form.qrCodeUrl\"\r\n            :limit=\"1\"\r\n            :fileSize=\"2\"\r\n            :isShowTip=\"true\"\r\n          />\r\n          <div class=\"form-tip\">\r\n            <p>• 支持 jpg、png、gif 格式</p>\r\n            <p>• 文件大小不超过 2MB</p>\r\n            <p>• 建议尺寸 200x200 像素，保证清晰度</p>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n          <el-input-number v-model=\"form.sortOrder\" :min=\"0\" placeholder=\"数字越小越靠前\" style=\"width: 100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listContact, getContact, delContact, addContact, updateContact, updateContactSort } from \"@/api/miniapp/contact\";\r\n\r\nexport default {\r\n  name: \"Contact\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 联系人管理表格数据\r\n      contactList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contactName: null,\r\n        contactCode: null,\r\n        contactPhone: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contactName: [\r\n          { required: true, message: \"联系人姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactCode: [\r\n          { required: true, message: \"联系人标识不能为空\", trigger: \"blur\" },\r\n          { pattern: /^[A-Z0-9_]{3,20}$/, message: \"标识只能包含大写字母、数字和下划线，长度3-20位\", trigger: \"blur\" }\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n          { pattern: /^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$|^400-?\\d{3}-?\\d{4}$/, message: \"请输入正确的电话号码\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询联系人管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listContact(this.queryParams).then(response => {\r\n        this.contactList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        contactId: null,\r\n        contactName: null,\r\n        contactCode: null,\r\n        contactPhone: null,\r\n        qrCodeUrl: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.contactId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加联系人管理\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const contactId = row.contactId || this.ids\r\n      getContact(contactId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改联系人管理\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.contactId != null) {\r\n            updateContact(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addContact(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const contactIds = row.contactId || this.ids;\r\n      this.$modal.confirm('是否确认删除联系人管理编号为\"' + contactIds + '\"的数据项？').then(function() {\r\n        return delContact(contactIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/contact/export', {\r\n        ...this.queryParams\r\n      }, `contact_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 排序变更处理 */\r\n    handleSortChange(row) {\r\n      const data = {\r\n        contactId: row.contactId,\r\n        sortOrder: row.sortOrder\r\n      };\r\n      updateContactSort(data).then(response => {\r\n        this.$modal.msgSuccess(\"排序更新成功\");\r\n        this.getList();\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"排序更新失败\");\r\n        this.getList(); // 重新加载以恢复原始值\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.no-qrcode {\r\n  color: #c0c4cc;\r\n  font-size: 12px;\r\n}\r\n\r\n.form-tip {\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.4;\r\n}\r\n\r\n.form-tip p {\r\n  margin: 2px 0;\r\n}\r\n</style>\r\n"]}]}