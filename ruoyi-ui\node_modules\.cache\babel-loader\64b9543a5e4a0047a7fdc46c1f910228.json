{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\demand.js", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\demand.js", "mtime": 1753759379310}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751858756942}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752653991061}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDemand", "query", "request", "url", "method", "data", "<PERSON><PERSON><PERSON><PERSON>", "demandId", "<PERSON><PERSON><PERSON><PERSON>", "updateDemand", "<PERSON><PERSON><PERSON><PERSON>", "demandIds", "ids", "Array", "isArray", "exportDemand", "offShelfDemand", "onShelfDemand", "increaseViewCount", "updateContactStatus"], "sources": ["D:/develop/javaProject/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/demand.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询需求列表\r\nexport function listDemand(query) {\r\n  return request({\r\n    url: '/miniapp/demand/list',\r\n    method: 'post',\r\n    data: query\r\n  })\r\n}\r\n\r\n// 查询需求详细\r\nexport function getDemand(demandId) {\r\n  return request({\r\n    url: '/miniapp/demand/getInfo',\r\n    method: 'post',\r\n    data: demandId\r\n  })\r\n}\r\n\r\n// 新增需求\r\nexport function addDemand(data) {\r\n  return request({\r\n    url: '/miniapp/demand/add',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改需求\r\nexport function updateDemand(data) {\r\n  return request({\r\n    url: '/miniapp/demand/edit',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除需求\r\nexport function delDemand(demandIds) {\r\n  // 确保传递的是数组格式\r\n  const ids = Array.isArray(demandIds) ? demandIds : [demandIds];\r\n  return request({\r\n    url: '/miniapp/demand/remove',\r\n    method: 'post',\r\n    data: ids\r\n  })\r\n}\r\n\r\n// 导出需求\r\nexport function exportDemand(query) {\r\n  return request({\r\n    url: '/miniapp/demand/export',\r\n    method: 'post',\r\n    data: query\r\n  })\r\n}\r\n\r\n// 下架需求\r\nexport function offShelfDemand(demandId) {\r\n  return request({\r\n    url: '/miniapp/demand/offShelf',\r\n    method: 'post',\r\n    data: demandId\r\n  })\r\n}\r\n\r\n// 上架需求\r\nexport function onShelfDemand(demandId) {\r\n  return request({\r\n    url: '/miniapp/demand/onShelf',\r\n    method: 'post',\r\n    data: demandId\r\n  })\r\n}\r\n\r\n// 增加浏览次数\r\nexport function increaseViewCount(demandId) {\r\n  return request({\r\n    url: '/miniapp/demand/app/increaseViewCount',\r\n    method: 'post',\r\n    data: demandId\r\n  })\r\n}\r\n\r\n// 更新联系状态\r\nexport function updateContactStatus(data) {\r\n  return request({\r\n    url: '/miniapp/docking/updateContactStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,SAASA,CAACH,IAAI,EAAE;EAC9B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACJ,IAAI,EAAE;EACjC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,SAAS,EAAE;EACnC;EACA,IAAMC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;EAC9D,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEO;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,YAAYA,CAACd,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,cAAcA,CAACT,QAAQ,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,aAAaA,CAACV,QAAQ,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,iBAAiBA,CAACX,QAAQ,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,mBAAmBA,CAACd,IAAI,EAAE;EACxC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}