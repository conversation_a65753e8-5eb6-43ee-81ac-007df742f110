import request from '@/utils/request'

// 查询西青金种子路演活动配置列表
export function listActivityConfig(query) {
  return request({
    url: '/miniapp/xiqing/activity-config/list',
    method: 'get',
    params: query
  })
}

// 查询西青金种子路演活动配置详细
export function getActivityConfig(activityId) {
  return request({
    url: '/miniapp/xiqing/activity-config/' + activityId,
    method: 'get'
  })
}

// 新增西青金种子路演活动配置
export function addActivityConfig(data) {
  return request({
    url: '/miniapp/xiqing/activity-config',
    method: 'post',
    data: data
  })
}

// 修改西青金种子路演活动配置
export function updateActivityConfig(data) {
  return request({
    url: '/miniapp/xiqing/activity-config',
    method: 'put',
    data: data
  })
}

// 删除西青金种子路演活动配置
export function delActivityConfig(activityIds) {
  return request({
    url: '/miniapp/xiqing/activity-config/' + activityIds,
    method: 'delete'
  })
}

// 导出西青金种子路演活动配置
export function exportActivityConfig(query) {
  return request({
    url: '/miniapp/xiqing/activity-config/export',
    method: 'get',
    params: query
  })
}
