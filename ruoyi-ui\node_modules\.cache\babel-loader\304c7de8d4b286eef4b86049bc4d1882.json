{"remainingRequest": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\contact.js", "dependencies": [{"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\contact.js", "mtime": 1753759379303}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751858756942}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752653983099}, {"path": "D:\\develop\\javaProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752653991061}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listContact", "query", "request", "url", "method", "data", "getContact", "contactId", "addContact", "updateContact", "delContact", "contactIds", "updateContactSort", "getEnabledContactList"], "sources": ["D:/develop/javaProject/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/contact.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询联系人管理列表\r\nexport function listContact(query) {\r\n  return request({\r\n    url: '/miniapp/contact/list',\r\n    method: 'post',\r\n    data: query\r\n  })\r\n}\r\n\r\n// 查询联系人管理详细\r\nexport function getContact(contactId) {\r\n  return request({\r\n    url: '/miniapp/contact/getInfo',\r\n    method: 'post',\r\n    data: contactId\r\n  })\r\n}\r\n\r\n// 新增联系人管理\r\nexport function addContact(data) {\r\n  return request({\r\n    url: '/miniapp/contact/add',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改联系人管理\r\nexport function updateContact(data) {\r\n  return request({\r\n    url: '/miniapp/contact/edit',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除联系人管理\r\nexport function delContact(contactIds) {\r\n  return request({\r\n    url: '/miniapp/contact/remove',\r\n    method: 'post',\r\n    data: contactIds\r\n  })\r\n}\r\n\r\n// 更新联系人排序\r\nexport function updateContactSort(data) {\r\n  return request({\r\n    url: '/miniapp/contact/updateSort',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 获取启用的联系人列表（小程序端调用）\r\nexport function getEnabledContactList() {\r\n  return request({\r\n    url: '/miniapp/contact/app/getEnabledList',\r\n    method: 'post'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACH,IAAI,EAAE;EAC/B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACJ,IAAI,EAAE;EAClC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,UAAU,EAAE;EACrC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEM;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACP,IAAI,EAAE;EACtC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}