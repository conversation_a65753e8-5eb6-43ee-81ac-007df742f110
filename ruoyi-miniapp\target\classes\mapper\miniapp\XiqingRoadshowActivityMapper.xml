<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.XiqingRoadshowActivityMapper">
    
    <resultMap type="XiqingRoadshowActivity" id="XiqingRoadshowActivityResult">
        <result property="activityId"    column="activity_id"    />
        <result property="activityTitle"    column="activity_title"    />
        <result property="activityDescription"    column="activity_description"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="maxParticipants"    column="max_participants"    />
        <result property="currentParticipants"    column="current_participants"    />
        <result property="formConfig"    column="form_config"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectXiqingRoadshowActivityVo">
        select activity_id, activity_title, activity_description, start_time, end_time, location, max_participants, current_participants, form_config, status, sort_order, create_by, create_time, update_by, update_time, remark from xiqing_roadshow_activity
    </sql>

    <select id="selectXiqingRoadshowActivityList" parameterType="XiqingRoadshowActivity" resultMap="XiqingRoadshowActivityResult">
        <include refid="selectXiqingRoadshowActivityVo"/>
        <where>
            <if test="activityTitle != null  and activityTitle != ''"> and activity_title like concat('%', #{activityTitle}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectXiqingRoadshowActivityByActivityId" parameterType="Long" resultMap="XiqingRoadshowActivityResult">
        <include refid="selectXiqingRoadshowActivityVo"/>
        where activity_id = #{activityId}
    </select>

    <select id="selectEnabledXiqingRoadshowActivityList" resultMap="XiqingRoadshowActivityResult">
        <include refid="selectXiqingRoadshowActivityVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertXiqingRoadshowActivity" parameterType="XiqingRoadshowActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into xiqing_roadshow_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">activity_title,</if>
            <if test="activityDescription != null">activity_description,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null">location,</if>
            <if test="maxParticipants != null">max_participants,</if>
            <if test="currentParticipants != null">current_participants,</if>
            <if test="formConfig != null">form_config,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">#{activityTitle},</if>
            <if test="activityDescription != null">#{activityDescription},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null">#{location},</if>
            <if test="maxParticipants != null">#{maxParticipants},</if>
            <if test="currentParticipants != null">#{currentParticipants},</if>
            <if test="formConfig != null">#{formConfig},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateXiqingRoadshowActivity" parameterType="XiqingRoadshowActivity">
        update xiqing_roadshow_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">activity_title = #{activityTitle},</if>
            <if test="activityDescription != null">activity_description = #{activityDescription},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>
            <if test="currentParticipants != null">current_participants = #{currentParticipants},</if>
            <if test="formConfig != null">form_config = #{formConfig},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where activity_id = #{activityId}
    </update>

    <update id="incrementParticipantCount" parameterType="Long">
        update xiqing_roadshow_activity 
        set current_participants = current_participants + 1,
            update_time = sysdate()
        where activity_id = #{activityId}
    </update>

    <delete id="deleteXiqingRoadshowActivityByActivityId" parameterType="Long">
        delete from xiqing_roadshow_activity where activity_id = #{activityId}
    </delete>

    <delete id="deleteXiqingRoadshowActivityByActivityIds" parameterType="String">
        delete from xiqing_roadshow_activity where activity_id in 
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
</mapper>
