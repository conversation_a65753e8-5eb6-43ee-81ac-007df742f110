import request from '@/utils/request'

// 查询联系人管理列表
export function listContact(query) {
  return request({
    url: '/miniapp/contact/list',
    method: 'post',
    data: query
  })
}

// 查询联系人管理详细
export function getContact(contactId) {
  return request({
    url: '/miniapp/contact/getInfo',
    method: 'post',
    data: contactId
  })
}

// 新增联系人管理
export function addContact(data) {
  return request({
    url: '/miniapp/contact/add',
    method: 'post',
    data: data
  })
}

// 修改联系人管理
export function updateContact(data) {
  return request({
    url: '/miniapp/contact/edit',
    method: 'post',
    data: data
  })
}

// 删除联系人管理
export function delContact(contactIds) {
  return request({
    url: '/miniapp/contact/remove',
    method: 'post',
    data: contactIds
  })
}

// 更新联系人排序
export function updateContactSort(data) {
  return request({
    url: '/miniapp/contact/updateSort',
    method: 'post',
    data: data
  })
}

// 获取启用的联系人列表（小程序端调用）
export function getEnabledContactList() {
  return request({
    url: '/miniapp/contact/app/getEnabledList',
    method: 'post'
  })
}
